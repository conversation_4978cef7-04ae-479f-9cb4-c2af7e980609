<!--
 * 外卖商家详情页面
 * 
 * 功能特性：
 * - 商家信息展示：完整的商家信息和营业状态
 * - 商品分类导航：左侧分类导航，右侧商品列表
 * - 商品详情弹窗：支持规格选择、套餐选择、数量调整
 * - 购物车管理：实时购物车状态和商品添加
 * - 促销活动：商家促销信息和商品优惠展示
 * - 响应式设计：适配移动端和桌面端
-->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '商家详情',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="merchant-detail-container">
    <!-- 自定义导航栏 -->
    <wd-navbar :title="merchant?.name || '商家详情'" />

    <!-- 商家头部信息 -->
    <view class="merchant-header">
      <!-- 商家封面图 -->
      <view class="merchant-cover">
        <image :src="merchant?.logo" mode="aspectFill" class="cover-image" />
        <view class="cover-overlay">
          <!-- 商家基本信息 -->
          <view class="merchant-basic-info">
            <view class="merchant-logo">
              <image :src="merchant?.logo" mode="aspectFill" />
            </view>
            <view class="merchant-info">
              <view class="merchant-name">{{ merchant?.name }}</view>
              <view class="merchant-rating">
                <wd-rate :value="merchant?.rating || 0" size="14" readonly />
                <text class="rating-text">{{ (merchant?.rating || 0).toFixed(1) }}</text>
                <text class="sales-text">月售{{ merchant?.month_sales || 0 }}</text>
              </view>
            </view>

            <!-- 收藏按钮 -->
            <view class="merchant-actions">
              <FavoriteButton
                v-if="merchant"
                :type="FavoriteType.MERCHANT"
                :target-id="merchant.id"
                :target-name="merchant.name"
                :target-image="merchant.logo"
                :extra-data="{
                  rating: merchant.rating,
                  delivery_fee: merchant.delivery_fee,
                  min_order_amount: merchant.min_order_amount,
                  delivery_time: merchant.delivery_time,
                }"
                :size="24"
                show-text
              />

              <!-- 测试历史记录按钮 (开发环境) -->
              <!-- <view v-if="isDev" class="dev-tools">
                <text class="login-status">
                  {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
                </text>
                <wd-button
                  v-if="merchant"
                  type="text"
                  size="small"
                  @click="testHistoryRecord"
                >
                  测试历史
                </wd-button>
              </view> -->
            </view>
          </view>

          <!-- 营业状态 -->
          <view
            class="operation-status"
            :class="{
              open: merchant?.operation_status === 1,
              closed: merchant?.operation_status !== 1,
            }"
          >
            {{ merchant?.operation_status === 1 ? '营业中' : '休息中' }}
          </view>
        </view>
      </view>

      <!-- 配送信息 -->
      <view class="delivery-info">
        <view class="delivery-item">
          <wd-icon name="time" size="14" color="#666" />
          <text>{{ merchant?.delivery_time || 30 }}分钟送达</text>
        </view>
        <view class="delivery-item">
          <wd-icon name="location" size="14" :color="distanceColor" />
          <text class="distance-text" :style="{ color: distanceColor }">{{ distanceText }}</text>
          <text class="delivery-separator">·</text>
          <wd-icon name="wallet" size="14" color="#666" />
          <text>{{ deliveryFeeText }}</text>
          <!-- 调试信息按钮 -->
          <view
            v-if="deliveryFeeResult && isDevelopment"
            class="debug-btn"
            @click="toggleDebugInfo"
          >
            <wd-icon name="info" size="12" color="#007AFF" />
          </view>
        </view>
        <view class="delivery-item">
          <wd-icon name="gift" size="14" color="#666" />
          <text>起送¥{{ merchant?.min_order_amount || 0 }}</text>
        </view>
      </view>

      <!-- 商家描述 -->
      <view v-if="merchant?.description" class="merchant-description">
        <text>{{ merchant.description }}</text>
      </view>

      <!-- 商家公告 -->
      <view v-if="merchant?.description" class="merchant-announcement">
        <wd-icon name="sound" size="14" color="#ff5500" />
        <text>{{ merchant.description }}</text>
      </view>

      <!-- 商家标签 -->
      <view v-if="merchant?.category_name" class="merchant-tags">
        <text class="tag">{{ merchant.category_name }}</text>
      </view>

      <!-- 联系方式 -->
      <view class="merchant-contact">
        <view class="contact-item" @click="contactMerchant">
          <wd-icon name="chat" size="16" color="#52c41a" />
          <text>联系商家</text>
        </view>
        <view class="contact-item" @click="callMerchant">
          <wd-icon name="phone" size="16" color="#ff9500" />
          <text>拨打电话</text>
        </view>
        <view class="contact-item">
          <wd-icon name="location" size="16" color="#1890ff" />
          <text>查看位置</text>
        </view>
      </view>
    </view>

    <!-- 配送费调试信息 -->
    <view v-if="showDebugInfo && deliveryFeeResult && isDevelopment" class="debug-info-panel">
      <view class="debug-header">
        <text class="debug-title">🚚 配送费计算调试信息</text>
        <view class="debug-actions">
          <wd-button size="small" type="primary" @click="copyDebugInfo">复制</wd-button>
          <wd-button size="small" @click="toggleDebugInfo">关闭</wd-button>
        </view>
      </view>

      <view class="debug-content">
        <view class="debug-summary">
          <view class="debug-item">
            <text class="debug-label">配送距离:</text>
            <text class="debug-value debug-distance">{{ distanceText }}</text>
          </view>
          <view class="debug-item">
            <text class="debug-label">最终配送费:</text>
            <text class="debug-value">¥{{ deliveryFeeResult.deliveryFee.toFixed(2) }}</text>
          </view>
          <view class="debug-item">
            <text class="debug-label">原始费用:</text>
            <text class="debug-value">¥{{ deliveryFeeResult.originalFee.toFixed(2) }}</text>
          </view>
          <view class="debug-item">
            <text class="debug-label">距离费用:</text>
            <text class="debug-value">
              ¥{{ (deliveryFeeResult.originalFee - (merchant?.delivery_fee || 0)).toFixed(2) }}
            </text>
          </view>
          <view class="debug-item">
            <text class="debug-label">免费配送:</text>
            <text
              class="debug-value"
              :class="{ 'debug-highlight': deliveryFeeResult.freeDelivery }"
            >
              {{ deliveryFeeResult.freeDelivery ? '是' : '否' }}
            </text>
          </view>
          <view class="debug-item">
            <text class="debug-label">享受折扣:</text>
            <text class="debug-value" :class="{ 'debug-highlight': deliveryFeeResult.discounted }">
              {{ deliveryFeeResult.discounted ? '是' : '否' }}
            </text>
          </view>
        </view>

        <view class="debug-logs">
          <text class="debug-logs-title">计算过程:</text>
          <view
            v-for="(log, index) in deliveryFeeResult.debugInfo"
            :key="index"
            class="debug-log-item"
          >
            <text class="debug-log-text">{{ log }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 分类导航 -->
      <scroll-view scroll-y class="category-nav" style="max-width: 30%">
        <view
          v-for="category in merchantCategories || []"
          :key="category.id"
          class="category-nav-item"
          :class="{ active: activeCategory === category.id }"
          @click="selectCategory(category.id)"
        >
          <text>{{ category.name }}</text>
          <view v-if="getCategoryFoodCount(category.id) > 0" class="category-count">
            {{ getCategoryFoodCount(category.id) }}
          </view>
        </view>
      </scroll-view>

      <!-- 商品列表 -->
      <scroll-view
        scroll-y
        class="food-scroll"
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="onLoadMore"
      >
        <view class="food-list">
          <view
            v-for="food in filteredFoods"
            :key="food.id"
            class="food-card"
            @click="handleFoodClick(food)"
          >
            <!-- 商品图片 -->
            <view class="food-image">
              <image :src="food.image" mode="aspectFill" />
              <view v-if="food.original_price > food.price" class="discount-badge">特价</view>
            </view>

            <!-- 商品信息 -->
            <view class="food-info">
              <view class="food-name">{{ food.name }}</view>
              <view v-if="food.brief" class="food-description">
                {{ food.brief }}
              </view>

              <!-- 销量和评分 -->
              <view class="food-stats">
                <text class="sales">月售{{ food.total_sold || 0 }}</text>
                <view class="rating">
                  <wd-rate :value="4.5" size="12" readonly />
                  <text>4.5</text>
                </view>
              </view>

              <!-- 包装费和备餐时间 -->
              <view class="food-extra-info">
                <text v-if="food.packaging_fee > 0" class="packaging-fee">
                  打包费¥{{ food.packaging_fee.toFixed(2) }}
                </text>
                <text v-if="food.preparation_time > 0" class="preparation-time">
                  {{ food.preparation_time }}分钟
                </text>
              </view>

              <!-- 单规格商品的价格和操作 -->
              <view
                v-if="!food.has_variants || !food.variants || food.variants.length === 0"
                class="food-price-action"
              >
                <view class="price-info">
                  <text class="current-price">¥{{ food.price.toFixed(2) }}</text>
                  <text
                    v-if="food.original_price && food.original_price > food.price"
                    class="original-price"
                  >
                    ¥{{ food.original_price.toFixed(2) }}
                  </text>
                </view>

                <!-- 添加到购物车按钮 -->
                <view class="add-to-cart">
                  <view v-if="getCartItemCount(food.id) > 0" class="quantity-control">
                    <view class="quantity-btn" @click.stop="decreaseQuantity(food)">
                      <wd-icon name="remove" size="14" color="#ff5500" />
                    </view>
                    <text class="quantity">{{ getCartItemCount(food.id) }}</text>
                    <view class="quantity-btn" @click.stop="increaseQuantity(food)">
                      <wd-icon name="add" size="14" color="#ff5500" />
                    </view>
                  </view>
                  <view v-else class="add-btn" @click.stop="addToCart(food)">
                    <wd-icon name="add" size="16" color="#fff" />
                  </view>
                </view>
              </view>

              <!-- 多规格商品的规格列表 -->
              <view
                v-if="food.has_variants && food.variants && food.variants.length > 0"
                class="variants-list"
              >
                <view v-for="variant in food.variants" :key="variant.id" class="variant-item">
                  <view class="variant-info">
                    <view class="variant-name">{{ variant.name }}</view>
                    <view class="variant-price">
                      <text class="current-price">¥{{ variant.price.toFixed(2) }}</text>
                      <text
                        v-if="variant.original_price && variant.original_price > variant.price"
                        class="original-price"
                      >
                        ¥{{ variant.original_price.toFixed(2) }}
                      </text>
                    </view>
                  </view>

                  <!-- 规格的数量控制 -->
                  <view class="variant-control">
                    <view
                      v-if="getCartItemCountWithVariant(food.id, variant.id) > 0"
                      class="quantity-control"
                    >
                      <view
                        class="quantity-btn"
                        @click.stop="decreaseVariantQuantity(food, variant)"
                      >
                        <wd-icon name="remove" size="14" color="#ff5500" />
                      </view>
                      <text class="quantity">
                        {{ getCartItemCountWithVariant(food.id, variant.id) }}
                      </text>
                      <view
                        class="quantity-btn"
                        @click.stop="increaseVariantQuantity(food, variant)"
                      >
                        <wd-icon name="add" size="14" color="#ff5500" />
                      </view>
                    </view>
                    <view v-else class="add-btn" @click.stop="addVariantToCart(food, variant)">
                      <wd-icon name="add" size="16" color="#fff" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 加载状态 -->
          <view v-if="false" class="loading-state">
            <wd-loading />
            <text>加载中...</text>
          </view>

          <!-- 无数据状态 -->
          <view
            v-if="!takeoutStore.foodsLoading && (!filteredFoods || filteredFoods.length === 0)"
            class="empty-state"
          >
            <image src="/static/images/empty-food.png" mode="aspectFit" class="empty-image" />
            <text class="empty-text">该分类暂无商品</text>
          </view>

          <!-- 加载完成状态 -->
          <view
            v-if="
              !takeoutStore.foodsLoading && filteredFoods && filteredFoods.length > 0 && !hasMore
            "
            class="load-complete"
          >
            <text>已加载全部商品</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 调试信息 -->
    <!-- <view v-if="isDev" class="debug-info" style="position: fixed; top: 100px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
      <div>总数量: {{ cartStats.total_quantity }}</div>
      <div>选中金额: {{ cartStats.selected_amount }}</div>
      <div>购物车项: {{ takeoutStore.cart?.items?.length || 0 }}</div>
    </view> -->

    <!-- 购物车栏 -->
    <view v-if="cartStats && cartStats.total_quantity > 0" class="cart-bar">
      <view class="cart-info" @click="goToCart">
        <view class="cart-icon">
          <wd-icon name="cart" size="24" color="#fff" />
          <view class="cart-badge">{{ cartStats?.total_quantity || 0 }}</view>
        </view>
        <view class="cart-price">
          <text class="total-price">¥{{ (cartStats?.selected_amount || 0).toFixed(2) }}</text>
          <text class="delivery-tip">
            {{
              (cartStats?.selected_amount || 0) >= (merchant?.min_order_amount || 0)
                ? '满足起送条件'
                : `还差¥${((merchant?.min_order_amount || 0) - (cartStats?.selected_amount || 0)).toFixed(2)}起送`
            }}
          </text>
        </view>
      </view>
      <view
        class="checkout-btn"
        :class="{ disabled: (cartStats.selected_amount || 0) < (merchant?.min_order_amount || 0) }"
        @click="goToCheckout"
      >
        <text>去结算</text>
      </view>
    </view>

    <!-- 商品详情弹窗 -->
    <wd-popup
      v-model="showFoodDetailPopup"
      position="bottom"
      :close-on-click-modal="true"
      custom-style="border-radius: 20px 20px 0 0; margin-bottom: 50px;"
    >
      <TakeoutFoodDetail
        v-if="selectedFood"
        :food="selectedFood"
        :is-popup="true"
        @close="closeFoodDetail"
        @add-to-cart="handlePopupAddToCart"
      />
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
/**
 * 外卖商家详情页面逻辑
 *
 * 主要功能：
 * - 商家详情信息展示
 * - 商品分类和列表展示
 * - 商品详情弹窗和规格选择
 * - 购物车管理和商品添加
 * - 分页加载和下拉刷新
 */

import { ref, computed, watch, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useTakeoutStore } from '@/store/takeout'
import { useAddressStore } from '@/store/address'
import FavoriteButton from '@/components/FavoriteButton.vue'
import TakeoutFoodDetail from '@/components/TakeoutFoodDetail.vue'
import { FavoriteType, HistoryType } from '@/api/user.typings'
import { addHistory } from '@/api/user'
import { useUserStore } from '@/store/user'
import { createConversation } from '@/api/chat'
import { ConversationType } from '@/api/chat.typings'
import type { Merchant, ITakeoutFood, TakeoutCategory } from '@/api/takeout.typings'
import {
  calculateDeliveryFee,
  formatDeliveryFeeText,
  type DeliveryFeeResult,
} from '@/utils/deliveryFeeCalculator'
import { formatDistance, getDistanceColor } from '@/utils/distanceFormatter'

// Store实例
const takeoutStore = useTakeoutStore()
const userStore = useUserStore()
const addressStore = useAddressStore()

// 开发环境检查
const isDevelopment = ref(true) // 暂时设为 true 用于调试

// 响应式状态
const merchantId = ref<number>(0)
const fromList = ref<boolean>(false) // 是否从列表页面跳转过来
const activeCategory = ref<number | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const refreshing = ref(false)
const hasMore = ref(true)

// 商品详情弹窗相关
const showFoodDetailPopup = ref(false)
const selectedFood = ref<ITakeoutFood | null>(null)
const selectedVariantId = ref<number | null>(null)
const selectedComboSelections = ref<Record<number, number[]>>({})
const selectedQuantity = ref(1)

// 配送费相关
const deliveryFeeResult = ref<DeliveryFeeResult | null>(null)
const deliveryFeeLoading = ref(false)

// 调试信息相关
const showDebugInfo = ref(false)

// 计算属性
const merchant = computed(() => takeoutStore.currentMerchant)
const merchantCategories = computed(() => takeoutStore.merchantCategories)
const foods = computed(() => takeoutStore.foods)
const foodStore = computed(() => takeoutStore)
const cartStats = computed(
  () =>
    takeoutStore.cartStats || {
      total_items: 0,
      total_quantity: 0,
      selected_items: 0,
      selected_quantity: 0,
      unselected_items: 0,
      unselected_quantity: 0,
      total_amount: 0,
      selected_amount: 0,
      merchant_count: 0,
    },
)

// 过滤后的商品列表
const filteredFoods = computed(() => {
  if (!foods.value || !Array.isArray(foods.value)) {
    return []
  }

  let foodList = foods.value
  if (activeCategory.value) {
    foodList = foodList.filter((food) => food.category_id === activeCategory.value)
  }

  return foodList
})

// 距离显示文本
const distanceText = computed(() => {
  if (deliveryFeeLoading.value) {
    return '计算中...'
  }

  if (deliveryFeeResult.value && deliveryFeeResult.value.distance > 0) {
    return formatDistance(deliveryFeeResult.value.distance)
  }

  // 检查是否有商家坐标信息
  if (merchant.value?.latitude && merchant.value?.longitude) {
    // 有商家坐标但没有用户地址
    if (!userStore.isLoggedIn) {
      return '请登录查看距离'
    } else {
      return '请设置收货地址'
    }
  }

  // 商家没有坐标信息
  return '距离未知'
})

// 距离显示颜色
const distanceColor = computed(() => {
  if (deliveryFeeResult.value && deliveryFeeResult.value.distance > 0) {
    return getDistanceColor(deliveryFeeResult.value.distance)
  }
  return '#666666' // 默认灰色
})

// 配送费显示文本
const deliveryFeeText = computed(() => {
  if (deliveryFeeLoading.value) {
    return '计算中...'
  }

  if (deliveryFeeResult.value) {
    return formatDeliveryFeeText(deliveryFeeResult.value)
  }

  // 降级显示：使用商家的固定配送费
  return `配送费¥${(merchant.value?.delivery_fee || 0).toFixed(2)}`
})

// 直接使用过滤后的商品列表，不展开规格
// 规格将在商品卡片内部显示

/**
 * 加载商家详情
 */
async function loadMerchantDetail() {
  if (!merchantId.value) return

  console.log('🔄 [Merchant] 开始加载商家详情:', {
    merchantId: merchantId.value,
    fromList: fromList.value,
    currentMerchant: takeoutStore.currentMerchant
      ? {
          id: takeoutStore.currentMerchant.id,
          name: takeoutStore.currentMerchant.name,
        }
      : null,
  })

  try {
    // 如果从列表页面跳转过来且store中已有商家数据，则不需要重新请求
    if (
      fromList.value &&
      takeoutStore.currentMerchant &&
      takeoutStore.currentMerchant.id === merchantId.value
    ) {
      console.log('✅ [Merchant] 使用已缓存的商家数据，无需重新请求')
      // 即使使用缓存数据，也要记录历史
      if (takeoutStore.currentMerchant) {
        recordMerchantHistory(takeoutStore.currentMerchant)
      }
    } else {
      console.log('📡 [Merchant] 从服务器获取商家详情')
      // 否则从服务器获取商家详情
      await takeoutStore.getMerchantDetailById(merchantId.value)
      console.log('✅ [Merchant] 商家详情获取成功:', takeoutStore.currentMerchant?.name)
      // 从服务器获取数据后记录历史
      if (takeoutStore.currentMerchant) {
        recordMerchantHistory(takeoutStore.currentMerchant)
      }
    }

    // 加载商家分类
    await loadMerchantCategories()

    // 默认选择第一个分类
    if (merchantCategories.value && merchantCategories.value.length > 0 && !activeCategory.value) {
      selectCategory(merchantCategories.value[0].id)
    }

    // 计算配送费
    await calculateMerchantDeliveryFee()
  } catch (error) {
    console.error('❌ [Merchant] 获取商家详情失败:', error)
    uni.showToast({
      title: '获取商家详情失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 计算商家配送费
 */
async function calculateMerchantDeliveryFee() {
  if (!merchant.value) {
    console.log('🚚 [DeliveryFee] 商家信息不存在，跳过配送费计算')
    return
  }

  try {
    deliveryFeeLoading.value = true
    console.log('🚚 [DeliveryFee] 开始计算商家配送费:', merchant.value.id)

    // 检查用户登录状态
    if (!userStore.isLoggedIn) {
      console.log('🚚 [DeliveryFee] 用户未登录，使用固定配送费')
      deliveryFeeResult.value = {
        deliveryFee: merchant.value.delivery_fee || 5.0,
        originalFee: merchant.value.delivery_fee || 5.0,
        distance: 0,
        freeDelivery: false,
        discounted: false,
        debugInfo: ['用户未登录，使用商家固定配送费'],
      }
      return
    }

    // 获取用户地址信息
    let userAddress = null
    try {
      // 首先尝试获取默认地址
      userAddress = await addressStore.fetchDefaultAddress()

      // 如果没有默认地址，尝试获取地址列表
      if (!userAddress) {
        console.log('🚚 [DeliveryFee] 用户未设置默认地址，尝试获取地址列表')
        const addressList = await addressStore.fetchAddressList()

        if (addressList && addressList.length > 0) {
          // 使用第一个地址作为计算地址
          userAddress = addressList[0]
          console.log('🚚 [DeliveryFee] 使用第一个地址进行配送费计算:', {
            id: userAddress.id,
            address: `${userAddress.province}${userAddress.city}${userAddress.district}${userAddress.detailed_address}`,
          })
        }
      }
    } catch (addressError) {
      console.warn('🚚 [DeliveryFee] 获取地址信息失败:', addressError)
    }

    if (!userAddress) {
      console.log('🚚 [DeliveryFee] 用户没有任何地址信息，使用固定配送费')
      deliveryFeeResult.value = {
        deliveryFee: merchant.value.delivery_fee || 5.0,
        originalFee: merchant.value.delivery_fee || 5.0,
        distance: 0,
        freeDelivery: false,
        discounted: false,
        debugInfo: ['用户没有任何地址信息，使用商家固定配送费'],
      }
      return
    }

    // 检查坐标信息
    const userLat = userAddress.location_latitude || userAddress.locationLatitude
    const userLng = userAddress.location_longitude || userAddress.locationLongitude
    const merchantLat = merchant.value.latitude || 0
    const merchantLng = merchant.value.longitude || 0

    if (!userLat || !userLng || !merchantLat || !merchantLng) {
      console.log('🚚 [DeliveryFee] 坐标信息不完整，使用固定配送费')
      deliveryFeeResult.value = {
        deliveryFee: merchant.value.delivery_fee || 5.0,
        originalFee: merchant.value.delivery_fee || 5.0,
        distance: 0,
        freeDelivery: false,
        discounted: false,
        debugInfo: ['坐标信息不完整，使用商家固定配送费'],
      }
      return
    }

    // 确定地址来源
    let addressSource: 'default' | 'first_available' | 'manual' = 'default'
    if (userAddress.id !== addressStore.defaultAddress?.id) {
      addressSource = 'first_available'
    }

    // 计算配送费
    const result = await calculateDeliveryFee({
      merchantId: merchant.value.id,
      merchantName: merchant.value.name,
      merchantLat,
      merchantLng,
      userLat,
      userLng,
      orderAmount: 0, // 商家详情页不考虑订单金额
      scenario: 'merchant_detail', // 标识为商家详情页场景
      addressInfo: {
        id: userAddress.id,
        isDefault: userAddress.is_default || userAddress.isDefault || false,
        address: `${userAddress.province}${userAddress.city}${userAddress.district}${userAddress.detailed_address}`,
        source: addressSource,
      },
    })

    deliveryFeeResult.value = result

    console.log('🚚 [DeliveryFee] 配送费计算完成:', {
      merchantId: merchant.value.id,
      deliveryFee: result.deliveryFee,
      distance: result.distance,
      freeDelivery: result.freeDelivery,
      discounted: result.discounted,
    })
  } catch (error) {
    console.error('🚚 [DeliveryFee] 计算配送费失败:', error)

    // 降级处理：使用商家固定配送费
    deliveryFeeResult.value = {
      deliveryFee: merchant.value.delivery_fee || 5.0,
      originalFee: merchant.value.delivery_fee || 5.0,
      distance: 0,
      freeDelivery: false,
      discounted: false,
      debugInfo: ['配送费计算失败，使用商家固定配送费'],
    }
  } finally {
    deliveryFeeLoading.value = false
  }
}

/**
 * 切换调试信息显示
 */
function toggleDebugInfo() {
  showDebugInfo.value = !showDebugInfo.value
}

/**
 * 复制调试信息到剪贴板
 */
async function copyDebugInfo() {
  if (!deliveryFeeResult.value) return

  try {
    const debugText = [
      '🚚 配送费计算调试信息',
      '='.repeat(30),
      ...deliveryFeeResult.value.debugInfo,
      '='.repeat(30),
      `最终结果: ¥${deliveryFeeResult.value.deliveryFee.toFixed(2)}`,
      `原始费用: ¥${deliveryFeeResult.value.originalFee.toFixed(2)}`,
      `配送距离: ${deliveryFeeResult.value.distance.toFixed(2)}km`,
      `免费配送: ${deliveryFeeResult.value.freeDelivery ? '是' : '否'}`,
      `享受折扣: ${deliveryFeeResult.value.discounted ? '是' : '否'}`,
      '',
      `生成时间: ${new Date().toLocaleString()}`,
    ].join('\n')

    // 使用uni-app的复制API
    await uni.setClipboardData({
      data: debugText,
    })

    uni.showToast({
      title: '调试信息已复制',
      icon: 'success',
      duration: 2000,
    })
  } catch (error) {
    console.error('复制调试信息失败:', error)
    uni.showToast({
      title: '复制失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

/**
 * 加载商家分类
 */
async function loadMerchantCategories() {
  if (!merchantId.value) return

  try {
    await takeoutStore.getMerchantCategoriesList(merchantId.value)
  } catch (error) {
    console.error('获取商家分类失败:', error)
  }
}

/**
 * 加载商品列表
 */
async function loadFoods() {
  if (!merchantId.value) return

  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value,
    }

    if (activeCategory.value) {
      params.category_id = activeCategory.value
    }

    const result: any = await takeoutStore.getMerchantFoods(merchantId.value, params)
    console.log('获取商品列表结果:', result)
    // 检查是否还有更多数据
    hasMore.value = result.list.length === pageSize.value
  } catch (error) {
    console.error('获取商品列表失败:', error)
    uni.showToast({
      title: '获取商品列表失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 选择分类
 */
function selectCategory(categoryId: number) {
  activeCategory.value = categoryId
  currentPage.value = 1
  hasMore.value = true
  loadFoods()
}

/**
 * 获取分类商品数量
 */
function getCategoryFoodCount(categoryId: number): number {
  if (!foods.value || !Array.isArray(foods.value)) {
    return 0
  }
  return foods.value.filter((food) => food.category_id === categoryId).length
}

/**
 * 下拉刷新
 */
async function onRefresh() {
  refreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  await Promise.all([loadMerchantDetail(), loadFoods()])
  refreshing.value = false
}

/**
 * 加载更多
 */
function onLoadMore() {
  if (!takeoutStore.foodsLoading && hasMore.value) {
    currentPage.value++
    loadFoods()
  }
}

/**
 * 处理商品点击事件
 */
function handleFoodClick(food: any) {
  console.log('🔧 [FoodClick] 商品点击事件:', {
    foodId: food.id,
    foodName: food.name,
    hasVariants: food.has_variants,
    hasCombos: food.is_combination,
    variantsCount: food.variants?.length || 0,
  })

  // 所有商品都显示详情弹窗，保持统一样式
  console.log('🔧 [FoodClick] 显示商品详情弹窗')
  showFoodDetail(food)
}

/**
 * 显示商品详情弹窗
 */
function showFoodDetail(food: ITakeoutFood) {
  selectedFood.value = food
  selectedComboSelections.value = {}

  // 对于单规格商品，从购物车中获取实际数量
  if (!food.has_variants || !food.variants || food.variants.length === 0) {
    const currentQuantity = getCartItemCount(food.id)
    selectedQuantity.value = currentQuantity > 0 ? currentQuantity : 1
    selectedVariantId.value = null
  } else {
    // 对于多规格商品，不需要预选规格和数量
    selectedVariantId.value = null
    selectedQuantity.value = 1
  }

  console.log('🔧 [FoodDetail] 显示商品详情弹窗:', {
    foodId: food.id,
    foodName: food.name,
    hasVariants: food.variants?.length > 0,
    hasCombos: food.is_combination,
    variantsCount: food.variants?.length || 0,
    selectedQuantity: selectedQuantity.value,
  })

  showFoodDetailPopup.value = true
}

/**
 * 关闭商品详情弹窗
 */
function closeFoodDetail() {
  showFoodDetailPopup.value = false
  selectedFood.value = null
  selectedVariantId.value = null
  selectedComboSelections.value = {}
  selectedQuantity.value = 1
}

/**
 * 处理弹窗中的添加到购物车
 */
const handlePopupAddToCart = async (data: {
  food: ITakeoutFood
  quantity: number
  variantId?: number
  comboSelections?: Record<number, number[]>
}) => {
  try {
    console.log('弹窗添加到购物车:', data)

    await takeoutStore.addToCart({
      food_id: data.food.id,
      variant_id: data.variantId || 0,
      quantity: data.quantity,
      remark: '',
      combo_selections: [], // 简化处理，暂时不处理套餐选择
    })

    uni.showToast({
      title: '已添加到购物车',
      icon: 'success',
    })

    // 关闭弹窗
    closeFoodDetail()
  } catch (error) {
    console.error('添加到购物车失败:', error)
    uni.showToast({
      title: '添加失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 选择规格
 */
function selectVariant(variantId: number) {
  selectedVariantId.value = variantId

  // 更新数量显示（根据新选择的规格）
  if (selectedFood.value) {
    const currentQuantity = getCartItemCountWithVariant(selectedFood.value.id, variantId)
    selectedQuantity.value = currentQuantity > 0 ? currentQuantity : 1

    console.log('🔧 [Variant] 选择规格:', {
      foodId: selectedFood.value.id,
      variantId,
      currentQuantity,
      selectedQuantity: selectedQuantity.value,
    })
  }
}

/**
 * 切换套餐选项
 */
function toggleComboOption(comboId: number, optionId: number, required: boolean) {
  if (!selectedComboSelections.value[comboId]) {
    selectedComboSelections.value[comboId] = []
  }

  const selections = selectedComboSelections.value[comboId]
  const index = selections.indexOf(optionId)

  if (required) {
    // 必选套餐，单选
    selectedComboSelections.value[comboId] = [optionId]
  } else {
    // 可选套餐，多选
    if (index > -1) {
      selections.splice(index, 1)
    } else {
      selections.push(optionId)
    }
  }
}

/**
 * 增加数量（普通商品）
 */
async function increaseQuantity(food?: any) {
  if (food) {
    console.log('➕ [Cart] 增加普通商品数量:', {
      foodId: food.id,
      foodName: food.name,
    })

    // 检查商品是否已经在购物车中（普通商品不考虑规格）
    const cartItems = takeoutStore.cart?.items
    const existingItem = cartItems?.find(
      (item) => item.food_id === food.id && !item.variant_id, // 普通商品没有规格
    )

    if (existingItem) {
      console.log('🔄 [Cart] 商品已存在，更新数量:', {
        cartItemId: existingItem.cart_item_id,
        currentQuantity: existingItem.quantity,
        newQuantity: existingItem.quantity + 1,
      })
      // 商品已存在，更新数量
      await takeoutStore.updateCartItem({
        CartItemID: existingItem.cart_item_id,
        quantity: existingItem.quantity + 1,
      })
    } else {
      console.log('➕ [Cart] 商品不存在，添加到购物车')
      // 商品不存在，添加到购物车
      await addToCart(food)
    }

    console.log('✅ [Cart] 商品数量增加完成，当前统计:', {
      cartStats: takeoutStore.cartStats,
      totalQuantity: takeoutStore.cartStats?.total_quantity || 0,
    })
  } else {
    // 弹窗中的数量增加
    if (selectedFood.value) {
      console.log('➕ [Popup] 弹窗中增加商品数量:', {
        foodId: selectedFood.value.id,
        currentQuantity: selectedQuantity.value,
      })

      // 检查商品是否已经在购物车中（需要考虑规格）
      const cartItems = takeoutStore.cart?.items
      const existingItem = cartItems?.find(
        (item) =>
          item.food_id === selectedFood.value!.id && item.variant_id === selectedVariantId.value,
      )

      if (existingItem) {
        // 商品已存在，更新数量
        await takeoutStore.updateCartItem({
          CartItemID: existingItem.cart_item_id,
          quantity: existingItem.quantity + 1,
        })
        selectedQuantity.value = existingItem.quantity + 1
      } else {
        // 商品不存在，添加到购物车
        const cartItem: any = {
          food_id: selectedFood.value.id,
          quantity: 1,
        }

        // 添加规格信息
        if (selectedVariantId.value) {
          cartItem.variant_id = selectedVariantId.value
        }

        // 添加套餐信息
        if (Object.keys(selectedComboSelections.value).length > 0) {
          cartItem.combo_selections = selectedComboSelections.value
        }

        await takeoutStore.addToCart(cartItem)
        selectedQuantity.value = 1
      }
    } else {
      // 如果没有选中商品，只增加显示数量
      selectedQuantity.value++
    }
  }
}

/**
 * 减少数量（普通商品）
 */
async function decreaseQuantity(food?: any) {
  if (food) {
    console.log('➖ [Cart] 减少普通商品数量:', {
      foodId: food.id,
      foodName: food.name,
    })

    // 从购物车中减少（普通商品不考虑规格）
    const cartItems = takeoutStore.cart?.items
    if (!cartItems || !Array.isArray(cartItems)) {
      return
    }

    const item = cartItems.find(
      (item) => item.food_id === food.id && !item.variant_id, // 普通商品没有规格
    )

    if (item && item.quantity > 1) {
      await takeoutStore.updateCartItem({
        CartItemID: item.cart_item_id,
        quantity: item.quantity - 1,
      })
    } else if (item) {
      await takeoutStore.removeCartItem(item.cart_item_id)
    }

    console.log('✅ [Cart] 商品数量减少完成，当前统计:', {
      cartStats: takeoutStore.cartStats,
      totalQuantity: takeoutStore.cartStats?.total_quantity || 0,
    })
  } else {
    // 弹窗中的数量减少
    if (selectedFood.value && selectedQuantity.value > 0) {
      console.log('➖ [Popup] 弹窗中减少商品数量:', {
        foodId: selectedFood.value.id,
        currentQuantity: selectedQuantity.value,
      })

      // 检查商品是否在购物车中（需要考虑规格）
      const cartItems = takeoutStore.cart?.items
      const existingItem = cartItems?.find(
        (item) =>
          item.food_id === selectedFood.value!.id && item.variant_id === selectedVariantId.value,
      )

      if (existingItem) {
        if (existingItem.quantity > 1) {
          // 减少数量
          await takeoutStore.updateCartItem({
            CartItemID: existingItem.cart_item_id,
            quantity: existingItem.quantity - 1,
          })
          selectedQuantity.value = existingItem.quantity - 1
        } else {
          // 移除商品
          await takeoutStore.removeCartItem(existingItem.cart_item_id)
          selectedQuantity.value = 0
        }
      } else {
        // 如果购物车中没有该商品，只减少显示数量
        if (selectedQuantity.value > 1) {
          selectedQuantity.value--
        }
      }
    }
  }
}

/**
 * 计算总价（单规格商品或有套餐的商品）
 */
function calculateTotalPrice(): number {
  if (!selectedFood.value) return 0

  let itemPrice = selectedFood.value.price

  // 如果选择了规格，使用规格价格
  if (selectedVariantId.value && selectedFood.value.variants) {
    const variant = selectedFood.value.variants.find((v) => v.id === selectedVariantId.value)
    if (variant) {
      itemPrice = variant.price
    }
  }

  // 添加套餐价格
  let comboPrice = 0
  if (selectedFood.value.combos) {
    selectedFood.value.combos.forEach((combo) => {
      const selections = selectedComboSelections.value[combo.id] || []
      selections.forEach((optionId) => {
        const option = combo.options.find((o) => o.id === optionId)
        if (option) {
          comboPrice += option.extra_price
        }
      })
    })
  }

  // 计算小计（商品价格 + 套餐价格）* 数量
  const subtotal = (itemPrice + comboPrice) * selectedQuantity.value

  // 添加包装费（包装费按数量计算）
  const packagingFee = (selectedFood.value.packaging_fee || 0) * selectedQuantity.value

  return subtotal + packagingFee
}

/**
 * 计算多规格商品的总计费用（包含包装费）
 */
function calculateVariantsTotalPrice(): number {
  if (!selectedFood.value || !selectedFood.value.variants) return 0

  const cartItems = takeoutStore.cart?.items || []
  let total = 0

  // 计算该商品所有规格在购物车中的总价
  selectedFood.value.variants.forEach((variant) => {
    const cartItem = cartItems.find(
      (item) => item.food_id === selectedFood.value!.id && item.variant_id === variant.id,
    )

    if (cartItem) {
      // 商品价格 * 数量
      const itemTotal = variant.price * cartItem.quantity
      // 包装费 * 数量
      const packagingFee = (selectedFood.value!.packaging_fee || 0) * cartItem.quantity
      total += itemTotal + packagingFee
    }
  })

  return total
}

/**
 * 计算多规格商品的总数量
 */
function calculateVariantsTotalQuantity(): number {
  if (!selectedFood.value || !selectedFood.value.variants) return 0

  const cartItems = takeoutStore.cart?.items || []
  let totalQuantity = 0

  // 计算该商品所有规格在购物车中的总数量
  selectedFood.value.variants.forEach((variant) => {
    const cartItem = cartItems.find(
      (item) => item.food_id === selectedFood.value!.id && item.variant_id === variant.id,
    )

    if (cartItem) {
      totalQuantity += cartItem.quantity
    }
  })

  return totalQuantity
}

/**
 * 获取购物车中商品数量
 */
function getCartItemCount(foodId: number): number {
  const cartItems = takeoutStore.cart?.items
  console.log('🛒 [Cart] 获取商品数量:', {
    foodId,
    cartItems: cartItems ? cartItems.length : 0,
    cart: takeoutStore.cart,
  })

  if (!cartItems || !Array.isArray(cartItems)) {
    console.log('⚠️ [Cart] 购物车数据为空或不是数组')
    return 0
  }

  const item = cartItems.find((item) => item.food_id === foodId)
  const quantity = item ? item.quantity : 0

  console.log('🛒 [Cart] 商品数量结果:', {
    foodId,
    found: !!item,
    quantity,
  })

  return quantity
}

/**
 * 获取购物车中指定规格商品数量
 */
function getCartItemCountWithVariant(foodId: number, variantId: number | null): number {
  const cartItems = takeoutStore.cart?.items

  if (!cartItems || !Array.isArray(cartItems)) {
    return 0
  }

  const item = cartItems.find((item) => item.food_id === foodId && item.variant_id === variantId)

  return item ? item.quantity : 0
}

/**
 * 获取商品在购物车中的数量（支持规格商品）
 */
function getCartItemCountForFood(food: any): number {
  if (food.is_variant_item) {
    // 规格商品：使用 food_id 和 variant_id
    return getCartItemCountWithVariant(food.food_id, food.variant_id)
  } else {
    // 普通商品：使用 food_id
    return getCartItemCount(food.food_id || food.id)
  }
}

/**
 * 增加规格商品数量
 */
async function increaseVariantQuantity(food: any, variant: any) {
  console.log('➕ [Variant] 增加规格商品数量:', {
    foodId: food.id,
    variantId: variant.id,
    variantName: variant.name,
  })

  // 检查商品是否已经在购物车中
  const cartItems = takeoutStore.cart?.items
  const existingItem = cartItems?.find(
    (item) => item.food_id === food.id && item.variant_id === variant.id,
  )

  if (existingItem) {
    // 商品已存在，更新数量
    await takeoutStore.updateCartItem({
      CartItemID: existingItem.cart_item_id,
      quantity: existingItem.quantity + 1,
    })
  } else {
    // 商品不存在，添加到购物车
    await addVariantToCart(food, variant)
  }
}

/**
 * 减少规格商品数量
 */
async function decreaseVariantQuantity(food: any, variant: any) {
  console.log('➖ [Variant] 减少规格商品数量:', {
    foodId: food.id,
    variantId: variant.id,
    variantName: variant.name,
  })

  // 从购物车中减少
  const cartItems = takeoutStore.cart?.items
  if (!cartItems || !Array.isArray(cartItems)) {
    return
  }

  const item = cartItems.find((item) => item.food_id === food.id && item.variant_id === variant.id)

  if (item && item.quantity > 1) {
    await takeoutStore.updateCartItem({
      CartItemID: item.cart_item_id,
      quantity: item.quantity - 1,
    })
  } else if (item) {
    await takeoutStore.removeCartItem(item.cart_item_id)
  }
}

/**
 * 添加规格商品到购物车
 */
async function addVariantToCart(food: any, variant: any) {
  try {
    console.log('🛒 [Variant] 添加规格商品到购物车:', {
      foodId: food.id,
      variantId: variant.id,
      variantName: variant.name,
      price: variant.price,
    })

    const cartItem: any = {
      food_id: food.id,
      variant_id: variant.id,
      quantity: 1,
    }

    const result = await takeoutStore.addToCart(cartItem)
    console.log('✅ [Variant] 规格商品添加完成，结果:', result)

    uni.showToast({
      title: '已添加到购物车',
      icon: 'success',
    })
  } catch (error) {
    console.error('❌ [Variant] 添加规格商品失败:', error)
    uni.showToast({
      title: '添加失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 添加到购物车（普通商品）
 */
async function addToCart(food: any) {
  try {
    console.log('🛒 [AddToCart] 开始添加普通商品到购物车:', {
      foodId: food.id,
      foodName: food.name,
      hasVariants: food.has_variants,
      hasCombos: food.is_combination,
    })

    // 检查商品是否有规格或套餐
    if (food.has_variants || food.is_combination) {
      console.log('🔧 [AddToCart] 商品有规格或套餐，显示详情弹窗')
      // 显示详情弹窗进行选择
      showFoodDetail(food)
      return
    }

    console.log('📤 [AddToCart] 普通商品，直接添加到购物车')
    // 直接添加到购物车
    const result = await takeoutStore.addToCart({
      food_id: food.id,
      quantity: 1,
    })

    console.log('✅ [AddToCart] Store 添加完成，结果:', result)

    uni.showToast({
      title: '已添加到购物车',
      icon: 'success',
    })
  } catch (error) {
    console.error('❌ [AddToCart] 添加到购物车失败:', error)
    uni.showToast({
      title: '添加失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 确认添加到购物车（弹窗中）
 */
async function confirmAddToCart() {
  if (!selectedFood.value) return

  try {
    // 检查必选套餐是否已选择
    if (selectedFood.value.combos) {
      for (const combo of selectedFood.value.combos) {
        if (
          combo.is_required &&
          (!selectedComboSelections.value[combo.id] ||
            selectedComboSelections.value[combo.id].length === 0)
        ) {
          uni.showToast({
            title: `请选择${combo.name}`,
            icon: 'none',
          })
          return
        }
      }
    }

    const cartItem: any = {
      food_id: selectedFood.value.id,
      quantity: selectedQuantity.value,
    }

    // 添加规格信息
    if (selectedVariantId.value) {
      cartItem.variant_id = selectedVariantId.value
    }

    // 添加套餐信息
    if (Object.keys(selectedComboSelections.value).length > 0) {
      cartItem.combo_selections = selectedComboSelections.value
    }

    await takeoutStore.addToCart(cartItem)

    uni.showToast({
      title: '已添加到购物车',
      icon: 'success',
    })

    closeFoodDetail()
  } catch (error) {
    console.error('添加到购物车失败:', error)
    uni.showToast({
      title: '添加失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 联系商家（聊天）
 */
async function contactMerchant() {
  try {
    // 检查用户是否登录
    const userStore = useUserStore()
    if (!userStore.isLoggedIn) {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
      })
      // 跳转到登录页面
      uni.navigateTo({
        url: '/pages/auth/login',
      })
      return
    }

    if (!merchant.value?.id) {
      uni.showToast({
        title: '商家信息不可用',
        icon: 'none',
      })
      return
    }

    uni.showLoading({
      title: '正在创建会话...',
    })

    // 创建与商家的聊天会话
    const conversation = await createConversation({
      type: ConversationType.MERCHANT,
      participantId: merchant.value.id.toString(),
      title: `与${merchant.value.name}的对话`,
      extra: {
        merchantId: merchant.value.id.toString(),
      },
    })

    console.log('🎯 [ContactMerchant] 创建会话成功:', conversation)
    console.log('🎯 [ContactMerchant] 会话ID:', conversation.id, '类型:', typeof conversation.id)

    uni.hideLoading()

    // 确保会话ID存在
    if (!conversation.id) {
      uni.showToast({
        title: '会话创建失败，请重试',
        icon: 'none',
      })
      return
    }

    // 跳转到聊天房间页面
    uni.navigateTo({
      url: `/pages/chat/room/index?id=${conversation.id}&type=merchant&name=${encodeURIComponent(merchant.value.name)}`,
    })
  } catch (error) {
    uni.hideLoading()
    console.error('创建商家聊天会话失败:', error)
    uni.showToast({
      title: '创建会话失败，请重试',
      icon: 'none',
    })
  }
}

/**
 * 拨打商家电话
 */
function callMerchant() {
  if (!merchant.value?.contact_mobile) {
    uni.showToast({
      title: '商家电话不可用',
      icon: 'none',
    })
    return
  }

  uni.makePhoneCall({
    phoneNumber: merchant.value.contact_mobile,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 跳转到购物车
 */
function goToCart() {
  uni.switchTab({
    url: '/pages/cart/index',
  })
}

/**
 * 跳转到结算页面
 */
function goToCheckout() {
  if ((cartStats.value.selected_amount || 0) < (merchant.value?.min_order_amount || 0)) {
    uni.showToast({
      title: '未满足起送条件',
      icon: 'none',
    })
    return
  }

  uni.navigateTo({
    url: '/pages/takeout/order-confirm',
  })
}

// 监听器
watch(activeCategory, () => {
  currentPage.value = 1
  hasMore.value = true
  loadFoods()
})

// 生命周期
onLoad((options) => {
  if (options.id) {
    merchantId.value = parseInt(options.id)
    // 检查是否从列表页面跳转过来
    fromList.value = options.fromList === '1'
    console.log('页面参数:', { id: options.id, fromList: fromList.value })
    loadMerchantDetail()
  }
})

onShow(async () => {
  console.log('📱 [Page] 页面显示，开始获取购物车数据')

  try {
    // 获取购物车数据和统计
    const cartData = await takeoutStore.getCart()
    console.log('🛒 [Cart] 购物车数据获取成功:', {
      cartData,
      itemsLength: cartData?.items?.length || 0,
      totalQuantity: cartData?.total_quantity || 0,
    })

    const cartStats = await takeoutStore.getCartStats()
    console.log('📊 [Cart] 购物车统计获取成功:', cartStats)

    // 检查 store 中的购物车数据
    console.log('🏪 [Store] Store中的购物车数据:', {
      cart: takeoutStore.cart,
      cartItems: takeoutStore.cart?.items,
      cartItemsLength: takeoutStore.cart?.items?.length || 0,
    })
  } catch (error) {
    console.error('❌ [Cart] 获取购物车数据失败:', error)
  }
})

onMounted(() => {
  // 初始化加载
  if (merchantId.value) {
    loadFoods()
  }
})

// 历史记录追踪
let historyTracker: any = null

// 记录商家浏览历史
const recordMerchantHistory = async (merchantData: Merchant) => {
  if (!userStore.isLoggedIn) {
    console.log('⚠️ [History] 用户未登录，跳过历史记录')
    return
  }

  if (!merchantId.value || !merchantData) {
    console.log('⚠️ [History] 商家数据或ID缺失，跳过历史记录')
    return
  }

  try {
    console.log('🎯 [History] 开始记录商家浏览历史')

    const request = {
      type: HistoryType.MERCHANT,
      target_id: merchantId.value,
      target_name: merchantData.name,
      target_image: merchantData.logo || '',
      extra_data: {
        rating: merchantData.rating,
        delivery_fee: merchantData.delivery_fee,
        min_order_amount: merchantData.min_order_amount,
        delivery_time: merchantData.delivery_time,
      },
      source: 'merchant_detail',
      duration: 0,
    }

    console.log('📤 [History] 发送历史记录请求:', request)
    await addHistory(request)
    console.log('✅ [History] 商家浏览历史记录成功')
  } catch (error) {
    console.error('❌ [History] 记录商家浏览历史失败:', error)
  }
}

// 当商家详情加载完成后记录历史
watch(merchant, (newMerchant) => {
  console.log('🏪 [Merchant] 商家数据变化:', {
    newMerchant: newMerchant
      ? {
          id: newMerchant.id,
          name: newMerchant.name,
          logo: newMerchant.logo,
        }
      : null,
    merchantId: merchantId.value,
  })

  if (newMerchant && merchantId.value > 0) {
    // 直接记录历史，不使用 composable
    recordMerchantHistory(newMerchant)
  } else {
    console.log('⚠️ [Merchant] 跳过历史记录:', {
      hasMerchant: !!newMerchant,
      merchantId: merchantId.value,
    })
  }
})

// 监听购物车统计变化
watch(
  cartStats,
  (newStats, oldStats) => {
    console.log('📊 [CartStats] 购物车统计数据变化:', {
      old: oldStats
        ? {
            totalQuantity: oldStats.total_quantity || 0,
            selectedAmount: oldStats.selected_amount || 0,
          }
        : null,
      new: newStats
        ? {
            totalQuantity: newStats.total_quantity || 0,
            selectedAmount: newStats.selected_amount || 0,
          }
        : null,
    })
  },
  { deep: true },
)

// 监听购物车数据变化，同步更新弹窗中的数量
watch(
  () => takeoutStore.cart?.items,
  (newItems) => {
    if (selectedFood.value && showFoodDetailPopup.value) {
      const currentQuantity = getCartItemCount(selectedFood.value.id)
      if (currentQuantity !== selectedQuantity.value) {
        console.log('🔄 [Popup] 同步弹窗数量:', {
          foodId: selectedFood.value.id,
          oldQuantity: selectedQuantity.value,
          newQuantity: currentQuantity,
        })
        selectedQuantity.value = currentQuantity > 0 ? currentQuantity : 1
      }
    }
  },
  { deep: true },
)

// 测试历史记录功能
const testHistoryRecord = async () => {
  if (!merchant.value || !merchantId.value) {
    uni.showToast({
      title: '商家数据未加载',
      icon: 'error',
    })
    return
  }

  console.log('🧪 [Test] 手动测试历史记录')

  try {
    const request = {
      type: HistoryType.MERCHANT,
      target_id: merchantId.value,
      target_name: merchant.value.name,
      target_image: merchant.value.logo || '',
      extra_data: {
        rating: merchant.value.rating,
        delivery_fee: merchant.value.delivery_fee,
        min_order_amount: merchant.value.min_order_amount,
        delivery_time: merchant.value.delivery_time,
      },
      user_agent: navigator.userAgent,
      platform: 'h5',
      source: 'manual_test',
      duration: 0,
    }

    console.log('📤 [Test] 发送测试请求:', request)
    console.log('📤 [Test] type 值:', request.type, '类型:', typeof request.type)
    console.log('📤 [Test] HistoryType.MERCHANT:', HistoryType.MERCHANT)

    await addHistory(request)

    uni.showToast({
      title: '历史记录测试成功',
      icon: 'success',
    })
    console.log('✅ [Test] 历史记录测试成功')
  } catch (error) {
    console.error('❌ [Test] 历史记录测试失败:', error)

    // 详细的错误信息
    let errorMessage = '未知错误'
    if (error && typeof error === 'object') {
      if (error.message) {
        errorMessage = error.message
      } else if (error.data && error.data.message) {
        errorMessage = error.data.message
      } else if (error.statusText) {
        errorMessage = error.statusText
      }
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    uni.showToast({
      title: `测试失败: ${errorMessage}`,
      icon: 'error',
    })
  }
}
</script>

<style lang="scss">
.merchant-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

// 商家头部信息
.merchant-header {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;

  .merchant-cover {
    position: relative;
    height: 200px;
    overflow: hidden;

    .cover-image {
      width: 100%;
      height: 100%;
    }

    .cover-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));

      .merchant-basic-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .merchant-logo {
          width: 60px;
          height: 60px;
          margin-right: 12px;
          border-radius: 8px;
          overflow: hidden;
          border: 2px solid #fff;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .merchant-info {
          flex: 1;

          .merchant-name {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 4px;
          }

          .merchant-rating {
            display: flex;
            align-items: center;

            .rating-text {
              margin-left: 4px;
              font-size: 14px;
              color: #fff;
              font-weight: 500;
            }

            .sales-text {
              margin-left: 8px;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .merchant-actions {
          display: flex;
          align-items: center;

          :deep(.favorite-button) {
            .favorite-text {
              color: rgba(255, 255, 255, 0.9);
              font-size: 12px;
            }
          }

          .dev-tools {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8rpx;
            margin-left: 16rpx;

            .login-status {
              font-size: 20rpx;
              color: rgba(255, 255, 255, 0.8);
              background-color: rgba(0, 0, 0, 0.3);
              padding: 4rpx 8rpx;
              border-radius: 4rpx;
            }
          }
        }
      }

      .operation-status {
        align-self: flex-start;
        padding: 4px 12px;
        font-size: 12px;
        border-radius: 12px;
        font-weight: 500;

        &.open {
          color: #52c41a;
          background-color: rgba(82, 196, 26, 0.1);
          border: 1px solid #52c41a;
        }

        &.closed {
          color: #ff4d4f;
          background-color: rgba(255, 77, 79, 0.1);
          border: 1px solid #ff4d4f;
        }
      }
    }
  }

  .delivery-info {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;

    .delivery-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      font-size: 13px;
      color: #666;
      position: relative;
      gap: 4px;

      text {
        margin-left: 4px;
      }
    }
  }

  .merchant-description {
    padding: 12px 16px;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    border-bottom: 1px solid #f0f0f0;
  }

  .merchant-announcement {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff7e6;
    border-bottom: 1px solid #f0f0f0;

    text {
      margin-left: 6px;
      font-size: 13px;
      color: #ff5500;
    }
  }

  .merchant-tags {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;

    .tag {
      margin-right: 8px;
      margin-bottom: 6px;
      padding: 4px 8px;
      font-size: 11px;
      color: #ff5500;
      background-color: #fff0e6;
      border-radius: 4px;
    }
  }

  .merchant-contact {
    display: flex;
    padding: 12px 16px;

    .contact-item {
      display: flex;
      align-items: center;
      margin-right: 24px;
      font-size: 14px;
      color: #666;
      padding: 8px 12px;
      border-radius: 16px;
      transition: all 0.2s ease;
      background: #f8f9fa;

      text {
        margin-left: 6px;
      }

      &:first-child {
        background: linear-gradient(135deg, #52c41a, #73d13d);
        color: white;
        font-weight: 500;

        &:active {
          transform: scale(0.95);
          background: linear-gradient(135deg, #389e0d, #52c41a);
        }
      }

      &:active {
        transform: scale(0.95);
        background: #e6f7ff;
      }
    }
  }
}

// 内容区域
.content-area {
  flex: 1;
  display: flex;
  height: 0;
}

// 分类导航
.category-nav {
  width: 50px;
  background-color: #f5f7fa;
  border-right: 1px solid #f0f0f0;

  .category-nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 8px;
    font-size: 13px;
    color: #666;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s;

    &.active {
      color: #ff5500;
      background-color: #fff;
      font-weight: 500;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 20px;
        background-color: #ff5500;
        border-radius: 0 2px 2px 0;
      }
    }

    .category-count {
      margin-top: 4px;
      padding: 2px 6px;
      font-size: 10px;
      color: #999;
      background-color: #f0f0f0;
      border-radius: 8px;
      min-width: 16px;
      text-align: center;
    }
  }
}

// 商品滚动区域
.food-scroll {
  flex: 1;
  background-color: #fff;
}

.food-list {
  padding: 12px;
}

// 商品卡片
.food-card {
  display: flex;
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }

  .food-image {
    position: relative;
    width: 80px;
    height: 80px;
    margin-right: 12px;
    border-radius: 6px;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }

    .discount-badge {
      position: absolute;
      top: 4px;
      left: 4px;
      padding: 2px 6px;
      font-size: 10px;
      color: #fff;
      background-color: #ff5500;
      border-radius: 4px;
    }
  }

  .food-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .food-name {
      font-size: 15px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .food-description {
      font-size: 12px;
      color: #999;
      margin-bottom: 6px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .food-stats {
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      .sales {
        font-size: 11px;
        color: #999;
        margin-right: 12px;
      }

      .rating {
        display: flex;
        align-items: center;
        font-size: 11px;
        color: #ff9500;

        text {
          margin-left: 2px;
        }
      }
    }

    .food-extra-info {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 11px;

      .packaging-fee {
        color: #666;
        margin-right: 12px;
        padding: 2px 6px;
        background-color: #f5f5f5;
        border-radius: 3px;
      }

      .preparation-time {
        color: #666;
        margin-right: 12px;
        padding: 2px 6px;
        background-color: #f0f8ff;
        border-radius: 3px;
      }

      .has-variants {
        color: #ff5500;
        padding: 2px 6px;
        background-color: #fff0e6;
        border-radius: 3px;
        font-weight: 500;
      }
    }

    .food-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 8px;

      .food-tag {
        margin-right: 6px;
        margin-bottom: 4px;
        padding: 2px 6px;
        font-size: 10px;
        color: #ff5500;
        background-color: #fff0e6;
        border-radius: 3px;
      }
    }

    .food-price-action {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .price-info {
        display: flex;
        align-items: center;

        .current-price {
          font-size: 16px;
          font-weight: 600;
          color: #ff5500;
        }

        .original-price {
          margin-left: 6px;
          font-size: 12px;
          color: #999;
          text-decoration: line-through;
        }
      }

      .add-to-cart {
        .quantity-control {
          display: flex;
          align-items: center;

          .quantity-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border: 1px solid #ff5500;
            border-radius: 50%;
            background-color: #fff;
          }

          .quantity {
            margin: 0 12px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            min-width: 20px;
            text-align: center;
          }
        }

        .add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          background-color: #ff5500;
          border-radius: 50%;
        }
      }
    }

    // 多规格商品的规格列表
    .variants-list {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      .variant-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-left: 3px solid #ff5500;
        padding-left: 12px;
        margin-bottom: 8px;
        background-color: #fafafa;
        border-radius: 6px;

        .variant-info {
          flex: 1;

          .variant-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .variant-price {
            display: flex;
            align-items: center;

            .current-price {
              font-size: 16px;
              font-weight: 600;
              color: #ff5500;
            }

            .original-price {
              margin-left: 6px;
              font-size: 12px;
              color: #999;
              text-decoration: line-through;
            }
          }
        }

        .variant-control {
          .quantity-control {
            display: flex;
            align-items: center;

            .quantity-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              border: 1px solid #ff5500;
              border-radius: 50%;
              background-color: #fff;
            }

            .quantity {
              margin: 0 12px;
              font-size: 14px;
              font-weight: 500;
              color: #333;
              min-width: 20px;
              text-align: center;
            }
          }

          .add-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background-color: #ff5500;
            border-radius: 50%;
          }
        }
      }
    }
  }
}

// 状态样式
.loading-state,
.empty-state,
.load-complete {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-state {
  text {
    margin-top: 12px;
    font-size: 14px;
    color: #999;
  }
}

.empty-state {
  .empty-image {
    width: 100px;
    height: 100px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 14px;
    color: #666;
  }
}

.load-complete {
  text {
    font-size: 14px;
    color: #999;
  }
}

// 购物车栏
.cart-bar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

  .cart-info {
    flex: 1;
    display: flex;
    align-items: center;

    .cart-icon {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;
      background-color: #ff5500;
      border-radius: 50%;
      margin-right: 12px;

      .cart-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        min-width: 18px;
        height: 18px;
        padding: 0 4px;
        font-size: 11px;
        color: #fff;
        text-align: center;
        line-height: 18px;
        background-color: #ff3b30;
        border-radius: 9px;
        font-weight: 500;
      }
    }

    .cart-price {
      display: flex;
      flex-direction: column;

      .total-price {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .delivery-tip {
        font-size: 11px;
        color: #999;
        margin-top: 2px;
      }
    }
  }

  .checkout-btn {
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    color: #fff;
    background-color: #ff5500;
    border-radius: 20px;
    transition: all 0.3s;

    &.disabled {
      background-color: #ccc;
      color: #999;
    }

    &:not(.disabled):active {
      transform: scale(0.95);
    }
  }
}

// 商品详情弹窗
.food-detail-popup {
  max-height: 70vh;
  background-color: #fff;
  margin-bottom: env(safe-area-inset-bottom);

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .popup-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .close-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #f5f7fa;
    }
  }

  .popup-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;

    .food-detail-image {
      width: 100%;
      height: 200px;
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .food-detail-info {
      margin-bottom: 20px;

      .food-detail-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .food-detail-description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 12px;
      }

      .food-detail-price {
        display: flex;
        align-items: center;

        .current-price {
          font-size: 20px;
          font-weight: 600;
          color: #ff5500;
        }

        .packaging-fee {
          font-size: 16px;
          font-weight: 500;
          color: #666;
          margin-left: 2px;
        }

        .original-price {
          margin-left: 8px;
          font-size: 14px;
          color: #999;
          text-decoration: line-through;
        }
      }
    }

    .variant-selection,
    .combo-selection,
    .quantity-selection {
      margin-bottom: 20px;

      .section-title {
        font-size: 15px;
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
      }
    }

    .variant-options {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .variant-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 16px;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        transition: all 0.3s;

        &.active {
          border-color: #ff5500;
          background-color: #fff0e6;
        }

        .variant-name {
          font-size: 13px;
          color: #333;
          margin-bottom: 4px;
        }

        .variant-price {
          font-size: 12px;
          color: #ff5500;
          font-weight: 500;
        }
      }
    }

    // 弹窗中的规格列表样式
    .popup-variants-list {
      .popup-variant-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        margin-bottom: 8px;
        border-left: 3px solid #ff5500;
        background-color: #fafafa;
        border-radius: 6px;

        .popup-variant-info {
          flex: 1;

          .popup-variant-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .popup-variant-price {
            display: flex;
            align-items: center;
            margin-bottom: 4px;

            .current-price {
              font-size: 16px;
              font-weight: 600;
              color: #ff5500;
            }

            .packaging-fee {
              font-size: 14px;
              font-weight: 500;
              color: #666;
              margin-left: 2px;
            }

            .original-price {
              margin-left: 6px;
              font-size: 12px;
              color: #999;
              text-decoration: line-through;
            }
          }
        }

        .popup-variant-control {
          .quantity-control {
            display: flex;
            align-items: center;

            .quantity-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              border: 1px solid #ff5500;
              border-radius: 50%;
              background-color: #fff;
            }

            .quantity {
              margin: 0 12px;
              font-size: 14px;
              font-weight: 500;
              color: #333;
              min-width: 20px;
              text-align: center;
            }
          }

          .add-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background-color: #ff5500;
            border-radius: 50%;
          }
        }
      }
    }

    .combo-options {
      .combo-option {
        margin-bottom: 16px;
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 8px;

        .combo-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .combo-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }

          .combo-required {
            font-size: 11px;
            color: #ff5500;
            background-color: #fff0e6;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }

        .combo-items {
          .combo-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin-bottom: 4px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            transition: all 0.3s;

            &.active {
              border-color: #ff5500;
              background-color: #fff0e6;
            }

            .item-name {
              font-size: 13px;
              color: #333;
            }

            .item-price {
              font-size: 12px;
              color: #ff5500;
              font-weight: 500;
            }
          }
        }
      }
    }

    .quantity-selection {
      .quantity-control {
        display: flex;
        align-items: center;

        .quantity-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border: 1px solid #ff5500;
          border-radius: 50%;
          background-color: #fff;
        }

        .quantity {
          margin: 0 20px;
          font-size: 16px;
          font-weight: 500;
          color: #333;
          min-width: 30px;
          text-align: center;
        }
      }
    }

    // 多规格商品数量显示
    .variants-quantity-display {
      margin-bottom: 20px;
      padding: 16px;
      border: 1px solid #ff5500;
      border-radius: 8px;
      background-color: #fff0e6;

      .section-title {
        font-size: 15px;
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
      }

      .variants-quantity-info {
        .quantity-display-control {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;

          .quantity-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 50%;
            background-color: #f5f5f5;

            &.disabled {
              cursor: not-allowed;
              opacity: 0.5;
            }
          }

          .quantity {
            margin: 0 20px;
            font-size: 18px;
            font-weight: 500;
            color: #333;
            min-width: 40px;
            text-align: center;
          }
        }

        .quantity-tip {
          text-align: center;

          text {
            font-size: 12px;
            color: #ff5500;
          }
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;

    .total-price {
      font-size: 16px;
      font-weight: 600;
      color: #ff5500;
    }

    .confirm-btn {
      padding: 12px 24px;
      font-size: 15px;
      font-weight: 500;
      color: #fff;
      background-color: #ff5500;
      border-radius: 20px;
      transition: all 0.3s;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .popup-tip {
    padding: 16px 20px;
    text-align: center;
    border-top: 1px solid #f0f0f0;

    text {
      font-size: 14px;
      color: #666;
    }
  }

  .popup-variants-total {
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;

    .variants-total-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .total-label {
        font-size: 15px;
        font-weight: 500;
        color: #333;
      }

      .total-amount {
        font-size: 18px;
        font-weight: 600;
        color: #ff5500;
      }
    }

    .variants-tip {
      text-align: center;

      text {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

/* 距离显示样式 */
.distance-text {
  font-weight: 500;
  font-size: 13px;
}

/* 距离和配送费分隔符样式 */
.delivery-separator {
  margin: 0 4px;
  color: #999;
  font-size: 12px;
}

/* 调试信息面板样式 */
.debug-btn {
  margin-left: 8px;
  padding: 2px 4px;
  cursor: pointer;
  border-radius: 4px;
  background-color: rgba(0, 122, 255, 0.1);
}

.debug-info-panel {
  margin: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #dee2e6;
}

.debug-title {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.debug-actions {
  display: flex;
  gap: 8px;
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.debug-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.debug-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.debug-value {
  font-size: 14px;
  color: #495057;
  font-weight: 600;
}

.debug-highlight {
  color: #28a745 !important;
}

.debug-distance {
  font-weight: 600;
  color: #007aff !important;
}

.debug-logs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-logs-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.debug-log-item {
  padding: 8px 12px;
  background-color: #ffffff;
  border-radius: 4px;
  border-left: 3px solid #007aff;
}

.debug-log-text {
  font-size: 12px;
  color: #495057;
  line-height: 1.4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
