<!--
 * 外卖购物车页面
 * 
 * 功能特性：
 * - 购物车商品展示：按商家分组显示购物车商品
 * - 商品管理：支持选择、数量调整、删除商品
 * - 价格计算：实时计算商品总价、配送费等
 * - 优惠券选择：支持选择可用优惠券
 * - 地址选择：支持选择收货地址
 * - 结算功能：跳转到订单确认页面
-->
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '购物车',
    navigationStyle: 'default',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="takeout-cart-container">
    <!-- 自定义导航栏 -->
    <wd-navbar title="购物车" :back="true" />

    <!-- 购物车内容 -->
    <view v-if="hasCartItems" class="cart-content">
      <!-- 收货地址选择 -->
      <view class="address-section" @click="selectAddress">
        <view v-if="selectedAddress" class="address-info">
          <wd-icon name="location" size="20" color="#ff5500" />
          <view class="address-details">
            <view class="address-name-phone">
              <text class="name">{{ selectedAddress.receiver_name }}</text>
              <text class="phone">{{ selectedAddress.receiver_mobile }}</text>
            </view>
            <view class="address-text">{{ selectedAddress.full_address }}</view>
          </view>
          <wd-icon name="arrow-right" size="16" color="#999" />
        </view>
        <view v-else class="no-address">
          <wd-icon name="location" size="20" color="#ff5500" />
          <text>请选择收货地址</text>
          <wd-icon name="arrow-right" size="16" color="#999" />
        </view>
      </view>

      <!-- 全选栏 -->
      <view class="select-all-bar">
        <wd-checkbox :model-value="isAllSelected" @update:model-value="handleSelectAll">
          全选
        </wd-checkbox>
        <view class="cart-count">共{{ cartStats.total_quantity }}件商品</view>
      </view>

      <!-- 商家信息 -->
      <view class="merchant-info">
        <view class="merchant-header">
          <image :src="cart.merchant_logo" class="merchant-logo" mode="aspectFill" />
          <view class="merchant-details">
            <text class="merchant-name">{{ cart.merchant_name }}</text>
            <view class="merchant-meta">
              <text class="delivery-time">30-45分钟</text>
              <text class="divider">|</text>
              <text class="delivery-fee">配送费¥{{ cart.delivery_fee.toFixed(2) }}</text>
            </view>
          </view>
          <view class="merchant-status">
            <text class="status-text open">营业中</text>
          </view>
        </view>
      </view>

      <!-- 购物车商品列表 -->
      <view class="cart-items">
        <view
          v-for="item in cart.items"
          :key="item.cart_item_id"
          class="cart-item"
          :class="{ disabled: false }"
        >
          <!-- 选择框 -->
          <view class="item-checkbox" @click.stop>
            <wd-checkbox
              :model-value="item.selected"
              :disabled="false"
              @update:model-value="handleItemSelect(item.cart_item_id, $event)"
            />
          </view>

          <!-- 商品信息 -->
          <view class="item-content">
            <image :src="item.food_image" class="item-image" mode="aspectFill" />

            <view class="item-info">
              <view class="item-title">{{ item.food_name }}</view>

              <!-- 规格信息 -->
              <view v-if="item.variant_name" class="item-specs">
                {{ item.variant_name }}
              </view>

              <!-- 备注信息 -->
              <view v-if="item.remark" class="item-remark">
                <text class="remark-label">备注:</text>
                <text class="remark-text">{{ item.remark }}</text>
              </view>

              <view class="item-footer">
                <view class="item-price">
                  <text class="current-price">¥{{ item.price.toFixed(2) }}</text>
                  <text
                    v-if="item.original_price && item.original_price > item.price"
                    class="original-price"
                  >
                    ¥{{ item.original_price.toFixed(2) }}
                  </text>
                </view>

                <view class="item-actions">
                  <!-- 数量控制 -->
                  <view class="quantity-control" :class="{ disabled: false }" @click.stop>
                    <view
                      class="quantity-btn decrease"
                      :class="{ disabled: item.quantity <= 1 }"
                      @click.stop="handleQuantityDecrease(item)"
                    >
                      <wd-icon name="remove" size="16" color="#666" />
                    </view>
                    <view class="quantity-input">
                      <text>{{ item.quantity }}</text>
                    </view>
                    <view
                      class="quantity-btn increase"
                      :class="{ disabled: false }"
                      @click.stop="handleQuantityIncrease(item)"
                    >
                      <wd-icon name="add" size="16" color="#666" />
                    </view>
                  </view>

                  <!-- 删除按钮 -->
                  <view class="delete-btn" @click.stop="handleDeleteItem(item.cart_item_id)">
                    <wd-icon name="delete" size="16" color="#ff4757" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 起送金额提示 -->
      <view v-if="cart.merchant_min_order_amount > selectedCartPrice" class="min-order-tip">
        <wd-icon name="info" size="16" color="#ff9500" />
        <text>还差¥{{ (cart.merchant_min_order_amount - selectedCartPrice).toFixed(2) }}起送</text>
      </view>

      <!-- 结算栏 -->
      <view class="checkout-bar">
        <view class="price-info">
          <view class="total-price">
            <text class="label">合计:</text>
            <text class="price">¥{{ selectedCartPrice.toFixed(2) }}</text>
          </view>
          <view class="price-detail">
            <text>已选{{ selectedCartCount }}件</text>
            <text v-if="cart.delivery_fee > 0">配送费¥{{ cart.delivery_fee.toFixed(2) }}</text>
          </view>
        </view>
        <view class="checkout-btn" :class="{ disabled: !canCheckout }" @click="handleCheckout">
          <text>去结算</text>
        </view>
      </view>
    </view>

    <!-- 空购物车状态 -->
    <view v-else class="empty-cart">
      <image src="/static/images/empty-cart.png" mode="aspectFit" class="empty-image" />
      <text class="empty-text">购物车空空如也</text>
      <text class="empty-tip">快去选购美食吧</text>
      <view class="go-shopping-btn" @click="goShopping">
        <text>去逛逛</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 外卖购物车页面逻辑
 *
 * 主要功能：
 * - 购物车商品展示和管理
 * - 商品选择和数量调整
 * - 价格计算和结算
 * - 地址选择
 */

import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useTakeoutStore } from '@/store/takeout'
import { useUserStore } from '@/store/user'
import type { TakeoutCartItem } from '@/api/takeout.typings'

// 临时定义地址类型
interface UserAddress {
  id: number
  receiver_name: string
  receiver_mobile: string
  full_address: string
}

// Store实例
const takeoutStore = useTakeoutStore()
const userStore = useUserStore()

// 响应式状态
const selectedAddress = ref<UserAddress | null>(null)
const loading = ref(false)

// 计算属性
const cart = computed(() => takeoutStore.cart)
const cartStats = computed(() => takeoutStore.cartStats)
const hasCartItems = computed(() => takeoutStore.hasCartItems)
const selectedCartItems = computed(() => takeoutStore.selectedCartItems)
const selectedCartCount = computed(() => takeoutStore.selectedCartCount)
const selectedCartPrice = computed(() => takeoutStore.selectedCartPrice)

// 是否全选
const isAllSelected = computed(() => {
  return cart.value.items.length > 0 && cart.value.items.every((item) => item.selected)
})

// 是否可以结算
const canCheckout = computed(() => {
  return (
    selectedCartItems.value.length > 0 &&
    selectedCartPrice.value >= cart.value.merchant_min_order_amount
  )
})

/**
 * 全选/取消全选
 */
async function handleSelectAll(selected: boolean) {
  try {
    const cartItemIds = cart.value.items.map((item) => item.cart_item_id)
    await takeoutStore.selectCartItems(cartItemIds, selected)
  } catch (error) {
    console.error('全选操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

/**
 * 选择/取消选择商品
 */
async function handleItemSelect(cartItemId: number, selected: boolean) {
  try {
    await takeoutStore.selectCartItems([cartItemId], selected)
  } catch (error) {
    console.error('选择商品失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

/**
 * 减少商品数量
 */
async function handleQuantityDecrease(item: TakeoutCartItem) {
  if (item.quantity <= 1) {
    return
  }

  try {
    await takeoutStore.updateCartItemQuantity(item.food_id, item.variant_id || 0, item.quantity - 1)
  } catch (error) {
    console.error('更新数量失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

/**
 * 增加商品数量
 */
async function handleQuantityIncrease(item: TakeoutCartItem) {
  try {
    await takeoutStore.updateCartItemQuantity(item.food_id, item.variant_id || 0, item.quantity + 1)
  } catch (error) {
    console.error('更新数量失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

/**
 * 删除商品
 */
async function handleDeleteItem(cartItemId: number) {
  try {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: async (res) => {
        if (res.confirm) {
          await takeoutStore.removeCartItem(cartItemId)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
        }
      },
    })
  } catch (error) {
    console.error('删除商品失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  }
}

/**
 * 选择收货地址
 */
function selectAddress() {
  uni.navigateTo({
    url: '/pages/address/list?from=takeout-cart',
  })
}

/**
 * 去结算
 */
function handleCheckout() {
  if (!canCheckout.value) {
    if (selectedCartItems.value.length === 0) {
      uni.showToast({
        title: '请选择要结算的商品',
        icon: 'none',
      })
    } else if (selectedCartPrice.value < cart.value.merchant_min_order_amount) {
      uni.showToast({
        title: `还差¥${(cart.value.merchant_min_order_amount - selectedCartPrice.value).toFixed(2)}起送`,
        icon: 'none',
      })
    }
    return
  }

  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择收货地址',
      icon: 'none',
    })
    return
  }

  // 跳转到订单确认页面
  uni.navigateTo({
    url: '/pages/takeout/order-confirm',
  })
}

/**
 * 去购物
 */
function goShopping() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

/**
 * 加载购物车数据
 */
async function loadCartData(forceRefresh = false) {
  try {
    loading.value = true
    console.log('🛒 [Cart Page] 开始加载购物车数据', { forceRefresh })

    // 强制刷新时传递参数
    await takeoutStore.getCart(forceRefresh)
    await takeoutStore.getCartStats()

    console.log('🛒 [Cart Page] 购物车数据加载完成')
  } catch (error) {
    console.error('加载购物车失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 加载用户默认地址
 */
async function loadDefaultAddress() {
  try {
    // 这里应该调用获取默认地址的API
    // const address = await userStore.getDefaultAddress()
    // selectedAddress.value = address
  } catch (error) {
    console.error('加载默认地址失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadCartData()
  loadDefaultAddress()
})

onShow(async () => {
  // 页面显示时强制刷新购物车数据
  console.log('🛒 [Cart Page] 页面显示，开始强制刷新购物车数据')

  // 强制刷新数据，不使用缓存
  await loadCartData(true)
})

// 监听购物车数据更新标记，当标记为true时主动刷新数据
watch(
  () => takeoutStore.cartDataUpdated,
  async (updated) => {
    if (updated) {
      console.log('🔄 [Cart Page] 检测到购物车数据已更新，主动刷新页面数据')
      await loadCartData(true)
      // 清除更新标记
      takeoutStore.clearCartDataUpdated()
    }
  },
  { immediate: true },
)

// 监听购物车数据更新事件
const handleCartDataUpdated = async (eventData: any) => {
  console.log('🎯 [Cart Page] 收到购物车数据更新事件:', eventData)

  // 强制刷新购物车数据
  await loadCartData(true)

  console.log('✅ [Cart Page] 购物车数据事件刷新完成')
}

// 在页面挂载时注册事件监听器
onMounted(() => {
  console.log('🎧 [Cart Page] 注册购物车数据更新事件监听器')
  uni.$on('cartDataUpdated', handleCartDataUpdated)
})

// 页面销毁时移除事件监听器
onUnmounted(() => {
  console.log('🧹 [Cart Page] 页面销毁，移除事件监听器')
  uni.$off('cartDataUpdated', handleCartDataUpdated)
})
</script>

<style lang="scss">
.takeout-cart-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px;
}

// 收货地址选择
.address-section {
  margin: 12px 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .address-info {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .address-details {
      flex: 1;
      margin-left: 12px;

      .address-name-phone {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-right: 12px;
        }

        .phone {
          font-size: 14px;
          color: #666;
        }
      }

      .address-text {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
      }
    }
  }

  .no-address {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #666;
    font-size: 16px;
  }
}

// 全选栏
.select-all-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 16px 12px;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .cart-count {
    font-size: 14px;
    color: #666;
  }
}

// 商家信息
.merchant-info {
  margin: 0 16px 12px;
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .merchant-header {
    display: flex;
    align-items: center;

    .merchant-logo {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      margin-right: 12px;
    }

    .merchant-details {
      flex: 1;

      .merchant-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .merchant-meta {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #666;

        .divider {
          margin: 0 8px;
        }
      }
    }

    .merchant-status {
      .status-text {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;

        &.open {
          color: #52c41a;
          background-color: #f6ffed;
        }
      }
    }
  }
}

// 购物车商品列表
.cart-items {
  flex: 1;
  margin: 0 16px;
}

.cart-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s;

  &.disabled {
    opacity: 0.6;
  }

  .item-checkbox {
    margin-right: 12px;
    padding-top: 4px;
  }

  .item-content {
    flex: 1;
    display: flex;

    .item-image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      margin-right: 12px;
    }

    .item-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.4;
      }

      .item-specs {
        font-size: 12px;
        color: #999;
        margin-bottom: 4px;
      }

      .item-remark {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;

        .remark-label {
          margin-right: 4px;
        }
      }

      .item-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .item-price {
          display: flex;
          align-items: center;

          .current-price {
            font-size: 16px;
            font-weight: 600;
            color: #ff5500;
            margin-right: 8px;
          }

          .original-price {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
          }
        }

        .item-actions {
          display: flex;
          align-items: center;

          .quantity-control {
            display: flex;
            align-items: center;
            margin-right: 12px;
            border: 1px solid #e8e8e8;
            border-radius: 16px;
            overflow: hidden;

            &.disabled {
              opacity: 0.5;
            }

            .quantity-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              background-color: #f8f8f8;
              transition: all 0.3s;

              &:active:not(.disabled) {
                background-color: #e8e8e8;
              }

              &.disabled {
                opacity: 0.5;
              }
            }

            .quantity-input {
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 40px;
              height: 32px;
              font-size: 14px;
              color: #333;
              background-color: #fff;
            }
          }

          .delete-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #fff2f0;
            transition: all 0.3s;

            &:active {
              background-color: #ffe7e6;
            }
          }
        }
      }
    }
  }
}

// 起送金额提示
.min-order-tip {
  display: flex;
  align-items: center;
  margin: 0 16px 12px;
  padding: 12px 16px;
  background-color: #fff7e6;
  border-radius: 8px;
  font-size: 14px;
  color: #ff9500;

  wd-icon {
    margin-right: 8px;
  }
}

// 结算栏
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);

  .price-info {
    flex: 1;

    .total-price {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      .label {
        font-size: 14px;
        color: #666;
        margin-right: 8px;
      }

      .price {
        font-size: 18px;
        font-weight: 600;
        color: #ff5500;
      }
    }

    .price-detail {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #999;

      text:not(:last-child) {
        margin-right: 12px;
      }
    }
  }

  .checkout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
    height: 44px;
    margin-left: 16px;
    background-color: #ff5500;
    border-radius: 22px;
    transition: all 0.3s;

    text {
      font-size: 16px;
      font-weight: 600;
      color: #fff;
    }

    &:active:not(.disabled) {
      background-color: #e64a00;
    }

    &.disabled {
      background-color: #ccc;
    }
  }
}

// 空购物车状态
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  .empty-image {
    width: 200px;
    height: 200px;
    margin-bottom: 20px;
  }

  .empty-text {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  .empty-tip {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
  }

  .go-shopping-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 40px;
    background-color: #ff5500;
    border-radius: 20px;
    transition: all 0.3s;

    text {
      font-size: 16px;
      font-weight: 500;
      color: #fff;
    }

    &:active {
      background-color: #e64a00;
    }
  }
}
</style>
