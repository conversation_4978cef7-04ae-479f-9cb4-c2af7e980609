/**
 * 外卖模块状态管理
 *
 * 该文件实现了外卖系统的状态管理，包括商家、外卖商品、购物车、订单等状态。
 * 提供响应式数据和操作方法，支持组件间的数据共享。
 */

import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import {
  getMerchantList,
  getMerchantDetail,
  getGlobalCategories,
  getMerchantCategories,
  getTakeoutFoodList,
  getMerchantFoodList,
  getTakeoutFoodDetail,
  addToTakeoutCart,
  updateTakeoutCartItem,
  removeTakeoutCartItem,
  getTakeoutCart,
  getTakeoutCartCount,
  selectTakeoutCartItems,
  createTakeoutOrder,
  getTakeoutOrderList,
  getTakeoutOrderDetail,
  cancelTakeoutOrder,
} from '@/api/takeout'
import type {
  Merchant,
  MerchantQueryRequest,
  TakeoutCategory,
  TakeoutFood,
  TakeoutFoodListQuery,
  TakeoutCartSummary,
  TakeoutCartItem,
  AddTakeoutToCartRequest,
  UpdateTakeoutCartItemRequest,
  SelectCartItemRequest,
  TakeoutOrder,
  CreateTakeoutOrderRequest,
  OrderListQuery,
  OrderStatus,
  MerchantResponseList,
  TakeoutFoodListResponse,
  TakeoutCategoryResponseList,
  TakeoutOrderListResponse,
} from '@/api/takeout.typings'

export const useTakeoutStore = defineStore('takeout', () => {
  // 商家相关状态
  const merchants = ref<Merchant[]>([])
  const currentMerchant = ref<Merchant | null>(null)
  const merchantsLoading = ref(false)
  const merchantsTotal = ref(0)
  const merchantsPage = ref(1)

  // 分类相关状态
  const categories = ref<TakeoutCategory[]>([])
  const globalCategories = ref<TakeoutCategory[]>([])
  const merchantCategories = ref<TakeoutCategory[]>([])
  const categoriesLoading = ref(false)

  // 外卖商品相关状态
  const foods = ref<TakeoutFood[]>([])
  const currentFood = ref<TakeoutFood | null>(null)
  const foodsLoading = ref(false)
  const foodsTotal = ref(0)
  const foodsPage = ref(1)

  // 购物车相关状态
  const cart = ref<TakeoutCartSummary>({
    items: [],
    total_items: 0,
    total_quantity: 0,
    total_amount: 0,
    original_amount: 0,
    total_discount: 0,
    total_packaging_fee: 0,
    delivery_fee: 0,
    merchant_id: 0,
    merchant_name: '',
    merchant_logo: '',
    merchant_min_order_amount: 0,
  })
  const cartLoading = ref(false)
  const cartStats = ref<{
    total_items: number
    total_quantity: number
    selected_items: number
    selected_quantity: number
    unselected_items: number
    unselected_quantity: number
    total_amount: number
    selected_amount: number
    merchant_count: number
  }>({
    total_items: 0,
    total_quantity: 0,
    selected_items: 0,
    selected_quantity: 0,
    unselected_items: 0,
    unselected_quantity: 0,
    total_amount: 0,
    selected_amount: 0,
    merchant_count: 0,
  })

  // 订单相关状态
  const orders = ref<TakeoutOrder[]>([])
  const currentOrder = ref<TakeoutOrder | null>(null)
  const ordersLoading = ref(false)
  const ordersTotal = ref(0)
  const ordersPage = ref(1)
  const orderStats = ref({
    total_orders: 0,
    pending_orders: 0,
    completed_orders: 0,
    cancelled_orders: 0,
    total_amount: 0,
  })

  // 根据购物车数据更新统计信息
  const updateCartStatsFromCartData = (cartData: TakeoutCartSummary) => {
    const selectedItems = cartData.items.filter((item) => item.selected)
    const unselectedItems = cartData.items.filter((item) => !item.selected)

    const newStats = {
      total_items: cartData.items.length,
      total_quantity: cartData.items.reduce((sum, item) => sum + item.quantity, 0),
      selected_items: selectedItems.length,
      selected_quantity: selectedItems.reduce((sum, item) => sum + item.quantity, 0),
      unselected_items: unselectedItems.length,
      unselected_quantity: unselectedItems.reduce((sum, item) => sum + item.quantity, 0),
      total_amount: cartData.items.reduce((sum, item) => sum + item.subtotal, 0),
      selected_amount: selectedItems.reduce((sum, item) => sum + item.subtotal, 0),
      merchant_count: new Set(cartData.items.map((item) => item.merchant_id)).size,
    }

    console.log('📊 [Store] 根据购物车数据计算统计:', {
      old: cartStats.value,
      new: newStats,
      changed: JSON.stringify(cartStats.value) !== JSON.stringify(newStats),
    })

    cartStats.value = newStats
  }

  // 计算属性
  const cartItemCount = computed(() => cart.value.total_quantity)
  const cartTotalPrice = computed(() => cart.value.total_amount)
  const hasCartItems = computed(() => cart.value.items.length > 0)
  const selectedCartItems = computed(() => cart.value.items.filter((item) => item.selected))
  const selectedCartCount = computed(() =>
    selectedCartItems.value.reduce((sum, item) => sum + item.quantity, 0),
  )
  const selectedCartPrice = computed(() =>
    selectedCartItems.value.reduce((sum, item) => sum + item.subtotal, 0),
  )

  // 获取指定商品和规格的购物车数量
  const getCartItemCount = (foodId: number): number => {
    const item = cart.value.items.find(
      (item) => item.food_id === foodId && (!item.variant_id || item.variant_id === 0),
    )
    return item ? item.quantity : 0
  }

  // 获取指定商品和规格变体的购物车数量
  const getCartItemCountWithVariant = (foodId: number, variantId: number): number => {
    const item = cart.value.items.find(
      (item) => item.food_id === foodId && item.variant_id === variantId,
    )
    return item ? item.quantity : 0
  }

  // 更新购物车商品数量
  const updateCartItemQuantity = async (foodId: number, variantId: number, quantity: number) => {
    try {
      console.log('🔄 [Store] 开始更新购物车商品数量:', { foodId, variantId, quantity })

      // 找到对应的购物车项
      let cartItem: any
      if (variantId === 0) {
        // 单规格商品：查找没有variant_id或variant_id为0的项
        cartItem = cart.value.items.find(
          (item) => item.food_id === foodId && (!item.variant_id || item.variant_id === 0),
        )
      } else {
        // 多规格商品：精确匹配variant_id
        cartItem = cart.value.items.find(
          (item) => item.food_id === foodId && item.variant_id === variantId,
        )
      }

      console.log('🔍 [Store] 查找购物车项结果:', {
        found: !!cartItem,
        cartItemId: cartItem?.cart_item_id,
        currentQuantity: cartItem?.quantity,
      })

      if (!cartItem) {
        console.warn('⚠️ [Store] 未找到对应的购物车项:', { foodId, variantId })
        return
      }

      if (quantity <= 0) {
        // 数量为0或负数，删除该项
        console.log('🗑️ [Store] 删除购物车项')
        await removeCartItem(cartItem.cart_item_id)
      } else {
        // 更新数量
        console.log('🔄 [Store] 更新购物车项数量')
        await updateCartItem({
          CartItemID: cartItem.cart_item_id,
          quantity: quantity,
          remark: '',
          combo_selections: [],
        })
      }

      // 刷新购物车数据
      console.log('🔄 [Store] 刷新购物车数据')
      await getCart()
    } catch (error) {
      console.error('❌ [Store] 更新购物车商品数量失败:', error)
      throw error
    }
  }

  // 商家相关操作
  const getMerchants = async (params?: MerchantQueryRequest) => {
    try {
      merchantsLoading.value = true
      const response = await getMerchantList(params || {})

      if (params?.page === 1) {
        merchants.value = response.list
      } else {
        merchants.value.push(...response.list)
      }

      merchantsTotal.value = response.total
      merchantsPage.value = params?.page || 1

      return response
    } catch (error) {
      console.error('获取商家列表失败:', error)
      throw error
    } finally {
      merchantsLoading.value = false
    }
  }

  const getMerchantDetailById = async (id: number) => {
    try {
      const merchant = await getMerchantDetail(id)
      currentMerchant.value = merchant
      return merchant
    } catch (error) {
      console.error('获取商家详情失败:', error)
      throw error
    }
  }

  const setCurrentMerchant = (merchant: Merchant) => {
    currentMerchant.value = merchant
  }

  const getMerchantCategoriesList = async (merchantId: number) => {
    try {
      categoriesLoading.value = true
      const response = await getMerchantCategories(merchantId)
      console.log('获取到的商家分类数据:', response)

      // 检查API返回的数据是否为null或undefined
      if (response && response.list && Array.isArray(response.list)) {
        merchantCategories.value = response.list
      } else {
        console.warn('⚠️ [Store] API返回的商家分类数据为空或格式不正确，使用空数组')
        merchantCategories.value = []
      }

      return response
    } catch (error) {
      console.error('获取商家分类失败:', error)
      // 发生错误时设置空数组
      merchantCategories.value = []
      throw error
    } finally {
      categoriesLoading.value = false
    }
  }

  // 分类相关操作
  const getGlobalCategoriesList = async () => {
    try {
      categoriesLoading.value = true
      const response = await getGlobalCategories()
      globalCategories.value = response.list
      categories.value = response.list
      return response
    } catch (error) {
      console.error('获取全局分类失败:', error)
      throw error
    } finally {
      categoriesLoading.value = false
    }
  }

  // 外卖商品相关操作
  const getMerchantFoods = async (
    merchantId: number,
    params?: {
      page?: number
      page_size?: number
      category_id?: number
    },
  ) => {
    try {
      foodsLoading.value = true
      const response = await getMerchantFoodList(merchantId, params)

      if (params?.page === 1) {
        foods.value = response.list || []
      } else {
        foods.value.push(...(response.list || []))
      }

      foodsTotal.value = response.total || 0
      foodsPage.value = params?.page || 1

      return response
    } catch (error) {
      console.error('获取商家商品失败:', error)
      throw error
    } finally {
      foodsLoading.value = false
    }
  }

  const getFoodDetail = async (id: number) => {
    try {
      const food = await getTakeoutFoodDetail(id)
      currentFood.value = food
      return food
    } catch (error) {
      console.error('获取商品详情失败:', error)
      throw error
    }
  }

  // 购物车相关操作
  const addToCart = async (params: AddTakeoutToCartRequest) => {
    try {
      console.log('🏪 [Store] 开始添加到购物车:', params)
      cartLoading.value = true

      console.log('📡 [Store] 调用 API 添加到购物车')
      const cartItem = await addToTakeoutCart(params)
      console.log('✅ [Store] API 调用成功，返回:', cartItem)

      console.log('🔄 [Store] 更新购物车数据和统计')
      const cartData = await getCart()
      console.log('🛒 [Store] 购物车数据更新完成:', {
        itemsCount: cartData?.items?.length,
        totalQuantity: cartData?.total_quantity,
      })

      console.log('📊 [Store] 购物车统计更新完成:', {
        totalQuantity: cartStats.value?.total_quantity,
        selectedAmount: cartStats.value?.selected_amount,
      })

      return cartItem
    } catch (error) {
      console.error('❌ [Store] 添加到购物车失败:', error)
      throw error
    } finally {
      cartLoading.value = false
    }
  }

  const updateCartItem = async (params: UpdateTakeoutCartItemRequest) => {
    try {
      console.log('🏪 [Store] 开始更新购物车项目:', params)
      cartLoading.value = true

      console.log('📡 [Store] 调用 API 更新购物车项目')
      const cartItem = await updateTakeoutCartItem(params)
      console.log('✅ [Store] API 调用成功，返回:', cartItem)

      console.log('🔄 [Store] 更新购物车数据和统计')
      const cartData = await getCart()
      console.log('🛒 [Store] 购物车数据更新完成:', {
        itemsCount: cartData?.items?.length,
        totalQuantity: cartData?.total_quantity,
      })

      console.log('📊 [Store] 购物车统计更新完成:', {
        totalQuantity: cartStats.value?.total_quantity,
        selectedAmount: cartStats.value?.selected_amount,
      })

      return cartItem
    } catch (error) {
      console.error('❌ [Store] 更新购物车失败:', error)
      throw error
    } finally {
      cartLoading.value = false
    }
  }

  const removeCartItem = async (cartItemId: number) => {
    try {
      cartLoading.value = true
      await removeTakeoutCartItem(cartItemId)

      // 更新购物车数据和统计
      await getCart()
    } catch (error) {
      console.error('删除购物车商品失败:', error)
      throw error
    } finally {
      cartLoading.value = false
    }
  }

  const getCart = async () => {
    try {
      console.log('🛒 [Store] 开始获取购物车数据')
      const cartData = await getTakeoutCart()
      console.log('🛒 [Store] API返回的购物车数据:', {
        items: cartData?.items?.length,
        totalQuantity: cartData?.total_quantity,
        firstItem: cartData?.items?.[0],
      })

      const oldCart = { ...cart.value }
      cart.value = cartData

      console.log('🛒 [Store] 购物车状态更新:', {
        oldItemsCount: oldCart.items?.length,
        newItemsCount: cart.value.items?.length,
        cartUpdated: cart.value !== oldCart,
      })

      // 根据购物车数据直接计算统计信息
      updateCartStatsFromCartData(cartData)

      return cartData
    } catch (error) {
      console.error('❌ [Store] 获取购物车失败:', error)
      throw error
    }
  }

  const getCartStats = async () => {
    try {
      console.log('📊 [Store] 开始获取购物车统计')
      const stats = await getTakeoutCartCount()
      console.log('📊 [Store] API 返回的统计数据:', stats)

      const oldStats = { ...cartStats.value }

      // 检查API返回的数据是否为null或undefined
      if (stats && typeof stats === 'object') {
        cartStats.value = stats
      } else {
        console.warn('⚠️ [Store] API返回的购物车统计数据为空，使用默认值')
        cartStats.value = {
          total_items: 0,
          total_quantity: 0,
          selected_items: 0,
          selected_quantity: 0,
          unselected_items: 0,
          unselected_quantity: 0,
          total_amount: 0,
          selected_amount: 0,
          merchant_count: 0,
        }
      }

      console.log('📊 [Store] 购物车统计更新:', {
        old: oldStats,
        new: cartStats.value,
        changed: JSON.stringify(oldStats) !== JSON.stringify(cartStats.value),
      })

      return cartStats.value
    } catch (error) {
      console.error('❌ [Store] 获取购物车统计失败:', error)
      // 发生错误时也设置默认值
      cartStats.value = {
        total_items: 0,
        total_quantity: 0,
        selected_items: 0,
        selected_quantity: 0,
        unselected_items: 0,
        unselected_quantity: 0,
        total_amount: 0,
        selected_amount: 0,
        merchant_count: 0,
      }
      throw error
    }
  }

  const selectCartItems = async (cartItemIds: number[], selected: boolean) => {
    try {
      await selectTakeoutCartItems({ cart_item_ids: cartItemIds, selected })

      // 更新本地状态
      cart.value.items.forEach((item) => {
        if (cartItemIds.includes(item.cart_item_id)) {
          item.selected = selected
        }
      })
    } catch (error) {
      console.error('选择购物车商品失败:', error)
      throw error
    }
  }

  // 订单相关操作
  const createOrder = async (params: CreateTakeoutOrderRequest) => {
    try {
      const result = await createTakeoutOrder(params)

      // 创建订单后清空购物车，更新数据和统计
      await getCart()

      return result
    } catch (error) {
      console.error('创建订单失败:', error)
      throw error
    }
  }

  const getOrders = async (params?: OrderListQuery) => {
    try {
      ordersLoading.value = true
      const response = await getTakeoutOrderList(params || {})

      if (params?.page === 1) {
        orders.value = response.list
      } else {
        orders.value.push(...response.list)
      }

      ordersTotal.value = response.total
      ordersPage.value = params?.page || 1

      return response
    } catch (error) {
      console.error('获取订单列表失败:', error)
      throw error
    } finally {
      ordersLoading.value = false
    }
  }

  const getOrderDetail = async (orderId: number) => {
    try {
      const order = await getTakeoutOrderDetail(orderId)
      currentOrder.value = order
      return order
    } catch (error) {
      console.error('获取订单详情失败:', error)
      throw error
    }
  }

  const getOrderStats = async () => {
    try {
      // 注意：这里需要根据实际后端接口调整，可能需要新增统计接口
      const response = await getTakeoutOrderList({ page: 1, page_size: 1 })
      const stats = {
        total_orders: response.total,
        pending_orders: 0,
        completed_orders: 0,
        cancelled_orders: 0,
        total_amount: 0,
      }
      orderStats.value = stats
      return stats
    } catch (error) {
      console.error('获取订单统计失败:', error)
      throw error
    }
  }

  const cancelOrder = async (orderId: number, reason?: string) => {
    try {
      await cancelTakeoutOrder(orderId, reason)

      // 更新本地订单状态
      const order = orders.value.find((o) => o.orderID === orderId)
      if (order) {
        order.orderStatus = 6 // CANCELLED
      }

      if (currentOrder.value?.orderID === orderId) {
        currentOrder.value.orderStatus = 6 // CANCELLED
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      throw error
    }
  }

  // 清空状态
  const clearMerchants = () => {
    merchants.value = []
    currentMerchant.value = null
    merchantsTotal.value = 0
    merchantsPage.value = 1
  }

  const clearFoods = () => {
    foods.value = []
    currentFood.value = null
    foodsTotal.value = 0
    foodsPage.value = 1
  }

  const clearCart = () => {
    cart.value = {
      items: [],
      total_items: 0,
      total_quantity: 0,
      total_amount: 0,
      original_amount: 0,
      total_discount: 0,
      total_packaging_fee: 0,
      delivery_fee: 0,
      merchant_id: 0,
      merchant_name: '',
      merchant_logo: '',
      merchant_min_order_amount: 0,
    }
    cartStats.value = {
      total_items: 0,
      total_quantity: 0,
      selected_items: 0,
      selected_quantity: 0,
      unselected_items: 0,
      unselected_quantity: 0,
      total_amount: 0,
      selected_amount: 0,
      merchant_count: 0,
    }
  }

  const clearOrders = () => {
    orders.value = []
    currentOrder.value = null
    ordersTotal.value = 0
    ordersPage.value = 1
  }

  const clearAll = () => {
    clearMerchants()
    clearFoods()
    clearCart()
    clearOrders()
    categories.value = []
    globalCategories.value = []
    merchantCategories.value = []
  }

  return {
    // 状态
    merchants,
    currentMerchant,
    merchantsLoading,
    merchantsTotal,
    merchantsPage,
    categories,
    globalCategories,
    merchantCategories,
    categoriesLoading,
    foods,
    currentFood,
    foodsLoading,
    foodsTotal,
    foodsPage,
    cart,
    cartLoading,
    cartStats,
    orders,
    currentOrder,
    ordersLoading,
    ordersTotal,
    ordersPage,
    orderStats,

    // 计算属性
    cartItemCount,
    cartTotalPrice,
    hasCartItems,
    selectedCartItems,
    selectedCartCount,
    selectedCartPrice,

    // 购物车数量查询方法
    getCartItemCount,
    getCartItemCountWithVariant,
    updateCartItemQuantity,

    // 操作方法
    getMerchants,
    getMerchantDetailById,
    setCurrentMerchant,
    getMerchantCategoriesList,
    getGlobalCategoriesList,
    getMerchantFoods,
    getFoodDetail,
    addToCart,
    updateCartItem,
    removeCartItem,
    getCart,
    getCartStats,
    selectCartItems,
    createOrder,
    getOrders,
    getOrderDetail,
    getOrderStats,
    cancelOrder,
    clearMerchants,
    clearFoods,
    clearCart,
    clearOrders,
    clearAll,
  }
})
